#!/usr/bin/env python3
"""
对比公平评估 vs 标准评估的差异
"""

import numpy as np
from utils import get_user_seqs
from calculate_metrics_from_matrix import calculate_metrics_from_score_matrix

def fair_vs_standard_evaluation():
    """对比公平评估和标准评估"""
    print("="*80)
    print("公平评估 vs 标准评估对比")
    print("="*80)
    
    # 1. 加载数据
    data_file = '../data/mooc.txt'
    user_seq, max_item, valid_rating_matrix, test_rating_matrix = get_user_seqs(data_file)
    
    # 2. 加载评分矩阵
    test_data = np.load('output/mooc_test_scores.npz')
    test_score_matrix = test_data['score_matrix']
    test_target_items = test_data['target_items']
    
    print(f"测试集评分矩阵: {test_score_matrix.shape}")
    print(f"验证集训练矩阵交互数: {valid_rating_matrix.nnz}")
    print(f"测试集训练矩阵交互数: {test_rating_matrix.nnz}")
    print(f"差异: {test_rating_matrix.nnz - valid_rating_matrix.nnz} (应该等于用户数35760)")
    
    # 3. 标准评估（当前做法）
    print(f"\n=== 标准评估（测试时过滤验证集）===")
    standard_results = calculate_metrics_from_score_matrix(
        test_score_matrix, 
        test_target_items, 
        test_rating_matrix  # 包含验证集的训练矩阵
    )
    
    print("标准评估结果:")
    for k in [5, 10, 20]:
        print(f"  HIT@{k}: {standard_results['hit'][k]:.4f}")
        print(f"  NDCG@{k}: {standard_results['ndcg'][k]:.4f}")
    
    # 4. 公平评估（测试时不过滤验证集）
    print(f"\n=== 公平评估（测试时不过滤验证集）===")
    fair_results = calculate_metrics_from_score_matrix(
        test_score_matrix, 
        test_target_items, 
        valid_rating_matrix  # 只用验证集的训练矩阵
    )
    
    print("公平评估结果:")
    for k in [5, 10, 20]:
        print(f"  HIT@{k}: {fair_results['hit'][k]:.4f}")
        print(f"  NDCG@{k}: {fair_results['ndcg'][k]:.4f}")
    
    # 5. 差异分析
    print(f"\n=== 差异分析 ===")
    print("标准评估 vs 公平评估的差异:")
    for k in [5, 10, 20]:
        hit_diff = standard_results['hit'][k] - fair_results['hit'][k]
        ndcg_diff = standard_results['ndcg'][k] - fair_results['ndcg'][k]
        hit_improvement = (hit_diff / fair_results['hit'][k]) * 100 if fair_results['hit'][k] > 0 else 0
        ndcg_improvement = (ndcg_diff / fair_results['ndcg'][k]) * 100 if fair_results['ndcg'][k] > 0 else 0
        
        print(f"HIT@{k}:")
        print(f"  标准: {standard_results['hit'][k]:.4f}")
        print(f"  公平: {fair_results['hit'][k]:.4f}")
        print(f"  差异: {hit_diff:+.4f} ({hit_improvement:+.1f}%)")
        
        print(f"NDCG@{k}:")
        print(f"  标准: {standard_results['ndcg'][k]:.4f}")
        print(f"  公平: {fair_results['ndcg'][k]:.4f}")
        print(f"  差异: {ndcg_diff:+.4f} ({ndcg_improvement:+.1f}%)")
        print()
    
    # 6. 排名分析
    print(f"=== 排名分析 ===")
    print(f"标准评估平均排名: {standard_results['avg_rank']:.2f}")
    print(f"公平评估平均排名: {fair_results['avg_rank']:.2f}")
    print(f"排名改善: {fair_results['avg_rank'] - standard_results['avg_rank']:.2f}")
    
    # 7. 详细用户分析
    analyze_user_differences(test_score_matrix, test_target_items, 
                           valid_rating_matrix, test_rating_matrix, 10)
    
    return standard_results, fair_results

def analyze_user_differences(score_matrix, target_items, valid_train_matrix, test_train_matrix, num_users=10):
    """分析用户级别的差异"""
    print(f"\n=== 前{num_users}个用户的详细对比 ===")
    
    # 准备两种过滤方式的评分矩阵
    fair_scores = score_matrix.copy()
    standard_scores = score_matrix.copy()
    
    # 过滤
    if hasattr(valid_train_matrix, 'toarray'):
        valid_interactions = valid_train_matrix.toarray()
        test_interactions = test_train_matrix.toarray()
    else:
        valid_interactions = valid_train_matrix
        test_interactions = test_train_matrix
    
    fair_scores[valid_interactions > 0] = 0  # 公平评估：只过滤验证集训练数据
    standard_scores[test_interactions > 0] = 0  # 标准评估：过滤测试集训练数据
    
    for i in range(min(num_users, len(target_items))):
        target_item = target_items[i][0] if target_items[i][0] != 0 else None
        if target_item is None:
            continue
        
        # 计算两种方式的排名
        fair_rank = np.sum(fair_scores[i] > fair_scores[i][target_item]) + 1
        standard_rank = np.sum(standard_scores[i] > standard_scores[i][target_item]) + 1
        
        # 检查目标物品是否在验证集中
        is_target_in_validation = valid_interactions[i][target_item] > 0
        
        # 计算被额外过滤的物品数
        extra_filtered = np.sum(test_interactions[i] > 0) - np.sum(valid_interactions[i] > 0)
        
        print(f"\n用户 {i+1}:")
        print(f"  目标物品ID: {target_item}")
        print(f"  目标物品在验证集中: {'是' if is_target_in_validation else '否'}")
        print(f"  额外过滤的物品数: {extra_filtered}")
        print(f"  公平评估排名: {fair_rank}")
        print(f"  标准评估排名: {standard_rank}")
        print(f"  排名改善: {fair_rank - standard_rank}")
        
        # 计算指标差异
        fair_hit5 = 1 if fair_rank <= 5 else 0
        standard_hit5 = 1 if standard_rank <= 5 else 0
        fair_hit10 = 1 if fair_rank <= 10 else 0
        standard_hit10 = 1 if standard_rank <= 10 else 0
        
        if fair_hit5 != standard_hit5 or fair_hit10 != standard_hit10:
            print(f"  ⚠️  指标差异: HIT@5 {fair_hit5}→{standard_hit5}, HIT@10 {fair_hit10}→{standard_hit10}")

def main():
    fair_vs_standard_evaluation()

if __name__ == "__main__":
    main()
