#!/bin/bash
# CoSeRec training script for Apple Silicon (M1/M2/M3/M4) with MPS support

echo "Starting CoSeRec training on Apple Silicon GPU (MPS)..."
echo "Dataset: Yelp"

python main.py \
    --data_name Yelp \
    --use_mps \
    --batch_size 128 \
    --epochs 200 \
    --lr 0.001 \
    --hidden_size 64 \
    --num_hidden_layers 2 \
    --max_seq_length 50 \
    --rec_weight 1.0 \
    --cf_weight 0.1 \
    --temperature 1.0 \
    --model_idx 0

echo "Training completed!"
