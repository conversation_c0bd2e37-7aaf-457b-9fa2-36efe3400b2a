#!/usr/bin/env python3
"""
直接从保存的评分矩阵计算HIT和NDCG指标
这样可以验证指标计算的正确性，并深入理解计算过程
"""

import numpy as np
import argparse

def calculate_metrics_from_score_matrix(score_matrix, target_items, train_matrix=None, k_list=[5, 10, 20]):
    """
    从评分矩阵直接计算HIT@K和NDCG@K指标

    Args:
        score_matrix: 形状为(num_users, num_items)的评分矩阵
        target_items: 每个用户的目标物品ID列表
        train_matrix: 训练集交互矩阵，用于过滤已知交互
        k_list: 要计算的K值列表

    Returns:
        dict: 包含各种指标的字典
    """
    num_users = score_matrix.shape[0]
    num_items = score_matrix.shape[1]

    print(f"评分矩阵形状: {score_matrix.shape}")
    print(f"用户数: {num_users}, 物品数: {num_items}")

    # 复制评分矩阵，避免修改原始数据
    filtered_score_matrix = score_matrix.copy()

    # 关键步骤：过滤掉训练集中的已知交互！
    if train_matrix is not None:
        print("正在过滤训练集中的已知交互...")
        if hasattr(train_matrix, 'toarray'):
            # 稀疏矩阵
            train_interactions = train_matrix.toarray()
        else:
            # 密集矩阵
            train_interactions = train_matrix

        # 将已知交互的评分设为0（这样它们就不会被推荐）
        filtered_score_matrix[train_interactions > 0] = 0
        print(f"过滤了 {np.sum(train_interactions > 0)} 个已知交互")
    else:
        print("警告：没有提供训练矩阵，未过滤已知交互！")

    # 存储结果
    hit_results = {k: 0 for k in k_list}
    ndcg_results = {k: 0.0 for k in k_list}

    valid_users = 0
    all_ranks = []

    # 对每个用户计算指标
    for user_idx in range(num_users):
        user_scores = filtered_score_matrix[user_idx]  # 使用过滤后的评分
        user_targets = target_items[user_idx]
        
        # 处理目标物品（可能有多个，但通常只有1个）
        for target_item in user_targets:
            if target_item == 0:  # 跳过padding
                continue
                
            valid_users += 1
            
            # 获取目标物品的评分
            target_score = user_scores[target_item]
            
            # 计算排名：有多少物品的评分比目标物品高
            rank = np.sum(user_scores > target_score) + 1
            all_ranks.append(rank)
            
            # 计算HIT@K和NDCG@K
            for k in k_list:
                if rank <= k:
                    hit_results[k] += 1
                    # NDCG计算：1 / log2(rank + 1)
                    ndcg_results[k] += 1.0 / np.log2(rank + 1)
    
    # 计算平均值
    for k in k_list:
        hit_results[k] = hit_results[k] / valid_users
        ndcg_results[k] = ndcg_results[k] / valid_users
    
    # 统计信息
    all_ranks = np.array(all_ranks)
    
    results = {
        'hit': hit_results,
        'ndcg': ndcg_results,
        'valid_users': valid_users,
        'avg_rank': np.mean(all_ranks),
        'median_rank': np.median(all_ranks),
        'min_rank': np.min(all_ranks),
        'max_rank': np.max(all_ranks),
        'all_ranks': all_ranks
    }
    
    return results

def analyze_ranking_distribution(ranks, k_list=[5, 10, 20]):
    """分析排名分布"""
    print(f"\n=== 排名分布分析 ===")
    print(f"总样本数: {len(ranks)}")
    print(f"平均排名: {np.mean(ranks):.2f}")
    print(f"中位数排名: {np.median(ranks):.2f}")
    print(f"最好排名: {np.min(ranks)}")
    print(f"最差排名: {np.max(ranks)}")
    
    # 排名区间分布
    bins = [1] + k_list + [50, 100, 500, 1000, np.inf]
    for i in range(len(bins)-1):
        if bins[i+1] == np.inf:
            count = np.sum(ranks >= bins[i])
            print(f"排名 {bins[i]}+: {count} ({count/len(ranks)*100:.1f}%)")
        else:
            count = np.sum((ranks >= bins[i]) & (ranks < bins[i+1]))
            print(f"排名 {bins[i]}-{bins[i+1]-1}: {count} ({count/len(ranks)*100:.1f}%)")

def load_and_calculate_metrics(npz_file, dataset_name, train_matrix=None):
    """加载NPZ文件并计算指标"""
    print(f"\n=== 加载{dataset_name}数据 ===")

    # 加载数据
    data = np.load(npz_file)
    score_matrix = data['score_matrix']
    user_ids = data['user_ids']
    target_items = data['target_items']

    print(f"评分矩阵形状: {score_matrix.shape}")
    print(f"用户ID数量: {len(user_ids)}")
    print(f"目标物品数量: {len(target_items)}")

    # 计算指标（加入训练矩阵过滤）
    results = calculate_metrics_from_score_matrix(score_matrix, target_items, train_matrix)
    
    print(f"\n=== {dataset_name}指标结果 ===")
    print(f"有效用户数: {results['valid_users']}")
    
    for k in [5, 10, 20]:
        hit_k = results['hit'][k]
        ndcg_k = results['ndcg'][k]
        print(f"HIT@{k}: {hit_k:.4f}")
        print(f"NDCG@{k}: {ndcg_k:.4f}")
    
    # 分析排名分布
    analyze_ranking_distribution(results['all_ranks'])
    
    return results

def compare_validation_test_metrics(valid_file, test_file):
    """对比验证集和测试集的指标"""
    print("="*80)
    print("从保存的评分矩阵计算指标")
    print("="*80)
    
    # 加载并计算验证集指标
    valid_results = load_and_calculate_metrics(valid_file, "验证集")
    
    # 加载并计算测试集指标
    test_results = load_and_calculate_metrics(test_file, "测试集")
    
    # 对比分析
    print(f"\n" + "="*60)
    print(f"验证集 vs 测试集 指标对比")
    print(f"="*60)
    
    print(f"平均排名对比:")
    print(f"  验证集: {valid_results['avg_rank']:.2f}")
    print(f"  测试集: {test_results['avg_rank']:.2f}")
    print(f"  差异: {test_results['avg_rank'] - valid_results['avg_rank']:.2f}")
    
    print(f"\n指标对比:")
    for k in [5, 10, 20]:
        valid_hit = valid_results['hit'][k]
        test_hit = test_results['hit'][k]
        valid_ndcg = valid_results['ndcg'][k]
        test_ndcg = test_results['ndcg'][k]
        
        print(f"HIT@{k}  - 验证集: {valid_hit:.4f}, 测试集: {test_hit:.4f}, 差异: {test_hit-valid_hit:+.4f}")
        print(f"NDCG@{k} - 验证集: {valid_ndcg:.4f}, 测试集: {test_ndcg:.4f}, 差异: {test_ndcg-valid_ndcg:+.4f}")
    
    # 分析现象
    print(f"\n=== 现象分析 ===")
    if test_results['avg_rank'] < valid_results['avg_rank']:
        print(f"✅ 测试集目标物品排名更靠前，这通常导致NDCG提升")
    else:
        print(f"❌ 验证集目标物品排名更靠前")
    
    # 分析Top-K命中率
    for k in [5, 10]:
        valid_topk = np.mean(valid_results['all_ranks'] <= k)
        test_topk = np.mean(test_results['all_ranks'] <= k)
        print(f"Top-{k}命中率 - 验证集: {valid_topk:.3f}, 测试集: {test_topk:.3f}")
    
    return valid_results, test_results

def detailed_user_analysis(npz_file, num_users=10):
    """详细分析前几个用户的评分情况"""
    data = np.load(npz_file)
    score_matrix = data['score_matrix']
    target_items = data['target_items']
    
    print(f"\n=== 前{num_users}个用户详细分析 ===")
    
    for i in range(min(num_users, len(target_items))):
        user_scores = score_matrix[i]
        target_item = target_items[i][0] if target_items[i][0] != 0 else None
        
        if target_item is None:
            continue
            
        # 目标物品评分和排名
        target_score = user_scores[target_item]
        rank = np.sum(user_scores > target_score) + 1
        
        # Top 5物品
        top5_indices = np.argsort(user_scores)[-5:][::-1]
        top5_scores = user_scores[top5_indices]
        
        print(f"\n用户 {i+1}:")
        print(f"  目标物品ID: {target_item}")
        print(f"  目标物品评分: {target_score:.4f}")
        print(f"  目标物品排名: {rank}")
        print(f"  Top 5物品: {list(zip(top5_indices, [f'{s:.4f}' for s in top5_scores]))}")
        
        # 计算这个用户的HIT和NDCG
        hit5 = 1 if rank <= 5 else 0
        hit10 = 1 if rank <= 10 else 0
        ndcg5 = (1.0 / np.log2(rank + 1)) if rank <= 5 else 0
        ndcg10 = (1.0 / np.log2(rank + 1)) if rank <= 10 else 0
        
        print(f"  HIT@5: {hit5}, HIT@10: {hit10}")
        print(f"  NDCG@5: {ndcg5:.4f}, NDCG@10: {ndcg10:.4f}")

def main():
    parser = argparse.ArgumentParser(description='从评分矩阵计算推荐指标')
    parser.add_argument('--valid_file', default='output/mooc_validation_scores.npz', 
                       help='验证集评分矩阵文件')
    parser.add_argument('--test_file', default='output/mooc_test_scores.npz', 
                       help='测试集评分矩阵文件')
    parser.add_argument('--detailed_analysis', action='store_true', 
                       help='是否进行详细的用户分析')
    
    args = parser.parse_args()
    
    # 主要对比分析
    valid_results, test_results = compare_validation_test_metrics(args.valid_file, args.test_file)
    
    # 详细用户分析（可选）
    if args.detailed_analysis:
        print("\n" + "="*60)
        print("详细用户分析")
        print("="*60)
        detailed_user_analysis(args.valid_file, 5)

if __name__ == "__main__":
    main()
