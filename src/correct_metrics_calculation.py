#!/usr/bin/env python3
"""
正确计算指标：包含训练集过滤的完整流程
"""

import numpy as np
from utils import get_user_seqs
from calculate_metrics_from_matrix import calculate_metrics_from_score_matrix
import argparse

def load_train_matrices(data_file):
    """加载训练集矩阵"""
    print("正在加载训练集矩阵...")
    user_seq, max_item, valid_rating_matrix, test_rating_matrix = get_user_seqs(data_file)
    
    print(f"验证集矩阵形状: {valid_rating_matrix.shape}")
    print(f"测试集矩阵形状: {test_rating_matrix.shape}")
    print(f"验证集交互数: {valid_rating_matrix.nnz}")
    print(f"测试集交互数: {test_rating_matrix.nnz}")
    
    return valid_rating_matrix, test_rating_matrix

def correct_metrics_calculation(data_name='mooc'):
    """正确计算指标的完整流程"""
    print("="*80)
    print("正确的指标计算（包含训练集过滤）")
    print("="*80)
    
    # 1. 加载训练集矩阵
    data_file = f'../data/{data_name}.txt'
    valid_train_matrix, test_train_matrix = load_train_matrices(data_file)
    
    # 2. 加载评分矩阵
    print("\n=== 加载评分矩阵 ===")
    valid_data = np.load(f'output/{data_name}_validation_scores.npz')
    test_data = np.load(f'output/{data_name}_test_scores.npz')
    
    valid_score_matrix = valid_data['score_matrix']
    valid_target_items = valid_data['target_items']
    
    test_score_matrix = test_data['score_matrix']
    test_target_items = test_data['target_items']
    
    print(f"验证集评分矩阵: {valid_score_matrix.shape}")
    print(f"测试集评分矩阵: {test_score_matrix.shape}")
    
    # 3. 计算验证集指标（使用验证集的训练矩阵）
    print(f"\n=== 计算验证集指标（正确方法）===")
    valid_results = calculate_metrics_from_score_matrix(
        valid_score_matrix, 
        valid_target_items, 
        valid_train_matrix  # 关键：使用对应的训练矩阵
    )
    
    print(f"验证集指标:")
    for k in [5, 10, 20]:
        print(f"  HIT@{k}: {valid_results['hit'][k]:.4f}")
        print(f"  NDCG@{k}: {valid_results['ndcg'][k]:.4f}")
    
    # 4. 计算测试集指标（使用测试集的训练矩阵）
    print(f"\n=== 计算测试集指标（正确方法）===")
    test_results = calculate_metrics_from_score_matrix(
        test_score_matrix, 
        test_target_items, 
        test_train_matrix  # 关键：使用对应的训练矩阵
    )
    
    print(f"测试集指标:")
    for k in [5, 10, 20]:
        print(f"  HIT@{k}: {test_results['hit'][k]:.4f}")
        print(f"  NDCG@{k}: {test_results['ndcg'][k]:.4f}")
    
    # 5. 对比官方结果
    print(f"\n=== 与官方结果对比 ===")
    print("官方测试集结果:")
    print("  HIT@5: 0.0904, NDCG@5: 0.0502")
    print("  HIT@10: 0.2361, NDCG@10: 0.0975") 
    print("  HIT@20: 0.3094, NDCG@20: 0.1160")
    
    print("我们计算的测试集结果:")
    print(f"  HIT@5: {test_results['hit'][5]:.4f}, NDCG@5: {test_results['ndcg'][5]:.4f}")
    print(f"  HIT@10: {test_results['hit'][10]:.4f}, NDCG@10: {test_results['ndcg'][10]:.4f}")
    print(f"  HIT@20: {test_results['hit'][20]:.4f}, NDCG@20: {test_results['ndcg'][20]:.4f}")
    
    # 计算差异
    official_results = {
        5: {'hit': 0.0904, 'ndcg': 0.0502},
        10: {'hit': 0.2361, 'ndcg': 0.0975},
        20: {'hit': 0.3094, 'ndcg': 0.1160}
    }
    
    print(f"\n差异分析:")
    for k in [5, 10, 20]:
        hit_diff = test_results['hit'][k] - official_results[k]['hit']
        ndcg_diff = test_results['ndcg'][k] - official_results[k]['ndcg']
        print(f"  HIT@{k}差异: {hit_diff:+.4f}")
        print(f"  NDCG@{k}差异: {ndcg_diff:+.4f}")
    
    # 6. 分析前几个用户的详细情况
    print(f"\n=== 详细用户分析（前5个用户）===")
    analyze_detailed_users(test_score_matrix, test_target_items, test_train_matrix, 5)
    
    return valid_results, test_results

def analyze_detailed_users(score_matrix, target_items, train_matrix, num_users=5):
    """分析前几个用户的详细计算过程"""
    
    # 过滤训练集交互
    filtered_scores = score_matrix.copy()
    if hasattr(train_matrix, 'toarray'):
        train_interactions = train_matrix.toarray()
    else:
        train_interactions = train_matrix
    
    filtered_scores[train_interactions > 0] = 0
    
    for i in range(min(num_users, len(target_items))):
        user_original_scores = score_matrix[i]
        user_filtered_scores = filtered_scores[i]
        target_item = target_items[i][0] if target_items[i][0] != 0 else None
        
        if target_item is None:
            continue
        
        # 计算排名
        original_rank = np.sum(user_original_scores > user_original_scores[target_item]) + 1
        filtered_rank = np.sum(user_filtered_scores > user_filtered_scores[target_item]) + 1
        
        # 找到被过滤的物品数量
        filtered_items = np.sum(train_interactions[i] > 0)
        
        print(f"\n用户 {i+1}:")
        print(f"  目标物品ID: {target_item}")
        print(f"  目标物品原始评分: {user_original_scores[target_item]:.4f}")
        print(f"  目标物品过滤后评分: {user_filtered_scores[target_item]:.4f}")
        print(f"  被过滤的训练物品数: {filtered_items}")
        print(f"  原始排名: {original_rank}")
        print(f"  过滤后排名: {filtered_rank}")
        
        # Top 5 推荐（过滤后）
        top5_indices = np.argsort(user_filtered_scores)[-5:][::-1]
        top5_scores = user_filtered_scores[top5_indices]
        print(f"  Top 5推荐: {list(zip(top5_indices, [f'{s:.4f}' for s in top5_scores]))}")
        
        # 计算指标
        hit5 = 1 if filtered_rank <= 5 else 0
        hit10 = 1 if filtered_rank <= 10 else 0
        ndcg5 = (1.0 / np.log2(filtered_rank + 1)) if filtered_rank <= 5 else 0
        ndcg10 = (1.0 / np.log2(filtered_rank + 1)) if filtered_rank <= 10 else 0
        
        print(f"  HIT@5: {hit5}, HIT@10: {hit10}")
        print(f"  NDCG@5: {ndcg5:.4f}, NDCG@10: {ndcg10:.4f}")

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--data_name', default='mooc', type=str)
    args = parser.parse_args()
    
    correct_metrics_calculation(args.data_name)

if __name__ == "__main__":
    main()
