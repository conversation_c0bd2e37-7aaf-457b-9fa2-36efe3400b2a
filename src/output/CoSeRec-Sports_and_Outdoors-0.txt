Namespace(data_dir='../data/', output_dir='output/', data_name='Sports_and_Outdoors', do_eval=False, model_idx=0, gpu_id='0', noise_ratio=0.0, training_data_ratio=1.0, augment_threshold=4, similarity_model_name='ItemCF_IUF', augmentation_warm_up_epoches=160, base_augment_type='random', augment_type_for_short='SIM', tao=0.2, gamma=0.7, beta=0.2, substitute_rate=0.1, insert_rate=0.4, max_insert_num_per_pos=1, temperature=1.0, n_views=2, model_name='CoSeRec', hidden_size=64, num_hidden_layers=2, num_attention_heads=2, hidden_act='gelu', attention_probs_dropout_prob=0.5, hidden_dropout_prob=0.5, initializer_range=0.02, max_seq_length=50, lr=0.001, batch_size=256, epochs=300, no_cuda=False, log_freq=1, seed=1, cf_weight=0.1, rec_weight=1.0, weight_decay=0.0, adam_beta1=0.9, adam_beta2=0.999, cuda_condition=False, data_file='../data/Sports_and_Outdoors.txt', item_size=18359, mask_id=18358, log_file='output/CoSeRec-Sports_and_Outdoors-0.txt')
{'epoch': 0, 'rec_avg_loss': '1.3588', 'joint_avg_loss': '5.1558', 'cl_avg_loss': '37.9700', 'cl_pair_0_loss': '37.9700'}
{'Epoch': 0, 'HIT@5': '0.0009', 'NDCG@5': '0.0004', 'HIT@10': '0.0022', 'NDCG@10': '0.0008', 'HIT@20': '0.0054', 'NDCG@20': '0.0016'}
Namespace(data_dir='../data/', output_dir='output/', data_name='Sports_and_Outdoors', do_eval=False, model_idx=0, gpu_id='0', noise_ratio=0.0, training_data_ratio=1.0, augment_threshold=4, similarity_model_name='ItemCF_IUF', augmentation_warm_up_epoches=160, base_augment_type='random', augment_type_for_short='SIM', tao=0.2, gamma=0.7, beta=0.2, substitute_rate=0.1, insert_rate=0.4, max_insert_num_per_pos=1, temperature=1.0, n_views=2, model_name='CoSeRec', hidden_size=64, num_hidden_layers=2, num_attention_heads=2, hidden_act='gelu', attention_probs_dropout_prob=0.5, hidden_dropout_prob=0.5, initializer_range=0.02, max_seq_length=50, lr=0.001, batch_size=256, epochs=300, no_cuda=False, log_freq=1, seed=1, cf_weight=0.1, rec_weight=1.0, weight_decay=0.0, adam_beta1=0.9, adam_beta2=0.999, device_type='mps', cuda_condition=False, data_file='../data/Sports_and_Outdoors.txt', item_size=18359, mask_id=18358, log_file='output/CoSeRec-Sports_and_Outdoors-0.txt')
Namespace(data_dir='../data/', output_dir='output/', data_name='Sports_and_Outdoors', do_eval=False, model_idx=0, gpu_id='0', noise_ratio=0.0, training_data_ratio=1.0, augment_threshold=4, similarity_model_name='ItemCF_IUF', augmentation_warm_up_epoches=160, base_augment_type='random', augment_type_for_short='SIM', tao=0.2, gamma=0.7, beta=0.2, substitute_rate=0.1, insert_rate=0.4, max_insert_num_per_pos=1, temperature=1.0, n_views=2, model_name='CoSeRec', hidden_size=64, num_hidden_layers=2, num_attention_heads=2, hidden_act='gelu', attention_probs_dropout_prob=0.5, hidden_dropout_prob=0.5, initializer_range=0.02, max_seq_length=50, lr=0.001, batch_size=256, epochs=300, no_cuda=False, log_freq=1, seed=1, cf_weight=0.1, rec_weight=1.0, weight_decay=0.0, adam_beta1=0.9, adam_beta2=0.999, device_type='mps', cuda_condition=False, data_file='../data/Sports_and_Outdoors.txt', item_size=18359, mask_id=18358, log_file='output/CoSeRec-Sports_and_Outdoors-0.txt')
{'epoch': 0, 'rec_avg_loss': '1.3573', 'joint_avg_loss': '5.0233', 'cl_avg_loss': '36.6601', 'cl_pair_0_loss': '36.6601'}
{'Epoch': 0, 'HIT@5': '0.0009', 'NDCG@5': '0.0004', 'HIT@10': '0.0037', 'NDCG@10': '0.0013', 'HIT@20': '0.0083', 'NDCG@20': '0.0024'}
{'epoch': 1, 'rec_avg_loss': '1.2979', 'joint_avg_loss': '2.6922', 'cl_avg_loss': '13.9438', 'cl_pair_0_loss': '13.9438'}
{'Epoch': 1, 'HIT@5': '0.0053', 'NDCG@5': '0.0027', 'HIT@10': '0.0093', 'NDCG@10': '0.0040', 'HIT@20': '0.0147', 'NDCG@20': '0.0054'}
{'epoch': 2, 'rec_avg_loss': '1.2645', 'joint_avg_loss': '2.1157', 'cl_avg_loss': '8.5122', 'cl_pair_0_loss': '8.5122'}
{'Epoch': 2, 'HIT@5': '0.0064', 'NDCG@5': '0.0032', 'HIT@10': '0.0108', 'NDCG@10': '0.0047', 'HIT@20': '0.0167', 'NDCG@20': '0.0061'}
{'epoch': 3, 'rec_avg_loss': '1.2421', 'joint_avg_loss': '1.9054', 'cl_avg_loss': '6.6329', 'cl_pair_0_loss': '6.6329'}
{'Epoch': 3, 'HIT@5': '0.0067', 'NDCG@5': '0.0036', 'HIT@10': '0.0114', 'NDCG@10': '0.0050', 'HIT@20': '0.0180', 'NDCG@20': '0.0067'}
{'epoch': 4, 'rec_avg_loss': '1.2295', 'joint_avg_loss': '1.7907', 'cl_avg_loss': '5.6125', 'cl_pair_0_loss': '5.6125'}
{'Epoch': 4, 'HIT@5': '0.0067', 'NDCG@5': '0.0043', 'HIT@10': '0.0118', 'NDCG@10': '0.0059', 'HIT@20': '0.0193', 'NDCG@20': '0.0078'}
{'epoch': 5, 'rec_avg_loss': '1.2192', 'joint_avg_loss': '1.7180', 'cl_avg_loss': '4.9880', 'cl_pair_0_loss': '4.9880'}
{'Epoch': 5, 'HIT@5': '0.0077', 'NDCG@5': '0.0050', 'HIT@10': '0.0123', 'NDCG@10': '0.0065', 'HIT@20': '0.0202', 'NDCG@20': '0.0085'}
{'epoch': 6, 'rec_avg_loss': '1.2089', 'joint_avg_loss': '1.6613', 'cl_avg_loss': '4.5244', 'cl_pair_0_loss': '4.5244'}
{'Epoch': 6, 'HIT@5': '0.0076', 'NDCG@5': '0.0049', 'HIT@10': '0.0130', 'NDCG@10': '0.0066', 'HIT@20': '0.0213', 'NDCG@20': '0.0087'}
{'epoch': 7, 'rec_avg_loss': '1.1988', 'joint_avg_loss': '1.6140', 'cl_avg_loss': '4.1515', 'cl_pair_0_loss': '4.1515'}
{'Epoch': 7, 'HIT@5': '0.0078', 'NDCG@5': '0.0050', 'HIT@10': '0.0131', 'NDCG@10': '0.0067', 'HIT@20': '0.0220', 'NDCG@20': '0.0089'}
{'epoch': 8, 'rec_avg_loss': '1.1881', 'joint_avg_loss': '1.5784', 'cl_avg_loss': '3.9029', 'cl_pair_0_loss': '3.9029'}
{'Epoch': 8, 'HIT@5': '0.0076', 'NDCG@5': '0.0049', 'HIT@10': '0.0134', 'NDCG@10': '0.0067', 'HIT@20': '0.0225', 'NDCG@20': '0.0090'}
{'epoch': 9, 'rec_avg_loss': '1.1798', 'joint_avg_loss': '1.5477', 'cl_avg_loss': '3.6785', 'cl_pair_0_loss': '3.6785'}
{'Epoch': 9, 'HIT@5': '0.0078', 'NDCG@5': '0.0051', 'HIT@10': '0.0138', 'NDCG@10': '0.0070', 'HIT@20': '0.0229', 'NDCG@20': '0.0093'}
{'epoch': 10, 'rec_avg_loss': '1.1692', 'joint_avg_loss': '1.5180', 'cl_avg_loss': '3.4887', 'cl_pair_0_loss': '3.4887'}
{'Epoch': 10, 'HIT@5': '0.0080', 'NDCG@5': '0.0051', 'HIT@10': '0.0143', 'NDCG@10': '0.0071', 'HIT@20': '0.0233', 'NDCG@20': '0.0093'}
{'epoch': 11, 'rec_avg_loss': '1.1604', 'joint_avg_loss': '1.4963', 'cl_avg_loss': '3.3592', 'cl_pair_0_loss': '3.3592'}
{'Epoch': 11, 'HIT@5': '0.0081', 'NDCG@5': '0.0052', 'HIT@10': '0.0141', 'NDCG@10': '0.0072', 'HIT@20': '0.0238', 'NDCG@20': '0.0096'}
{'epoch': 12, 'rec_avg_loss': '1.1518', 'joint_avg_loss': '1.4710', 'cl_avg_loss': '3.1920', 'cl_pair_0_loss': '3.1920'}
{'Epoch': 12, 'HIT@5': '0.0083', 'NDCG@5': '0.0053', 'HIT@10': '0.0143', 'NDCG@10': '0.0072', 'HIT@20': '0.0246', 'NDCG@20': '0.0098'}
{'epoch': 13, 'rec_avg_loss': '1.1429', 'joint_avg_loss': '1.4461', 'cl_avg_loss': '3.0320', 'cl_pair_0_loss': '3.0320'}
{'Epoch': 13, 'HIT@5': '0.0082', 'NDCG@5': '0.0053', 'HIT@10': '0.0146', 'NDCG@10': '0.0074', 'HIT@20': '0.0247', 'NDCG@20': '0.0099'}
{'epoch': 14, 'rec_avg_loss': '1.1376', 'joint_avg_loss': '1.4268', 'cl_avg_loss': '2.8928', 'cl_pair_0_loss': '2.8928'}
{'Epoch': 14, 'HIT@5': '0.0082', 'NDCG@5': '0.0054', 'HIT@10': '0.0149', 'NDCG@10': '0.0075', 'HIT@20': '0.0253', 'NDCG@20': '0.0101'}
{'epoch': 15, 'rec_avg_loss': '1.1318', 'joint_avg_loss': '1.4148', 'cl_avg_loss': '2.8309', 'cl_pair_0_loss': '2.8309'}
{'Epoch': 15, 'HIT@5': '0.0085', 'NDCG@5': '0.0055', 'HIT@10': '0.0151', 'NDCG@10': '0.0076', 'HIT@20': '0.0259', 'NDCG@20': '0.0103'}
{'epoch': 16, 'rec_avg_loss': '1.1230', 'joint_avg_loss': '1.3932', 'cl_avg_loss': '2.7024', 'cl_pair_0_loss': '2.7024'}
{'Epoch': 16, 'HIT@5': '0.0090', 'NDCG@5': '0.0057', 'HIT@10': '0.0157', 'NDCG@10': '0.0079', 'HIT@20': '0.0271', 'NDCG@20': '0.0107'}
