Namespace(data_dir='../data/', output_dir='output/', data_name='mooc', do_eval=False, model_idx=0, gpu_id='0', noise_ratio=0.0, training_data_ratio=1.0, augment_threshold=4, similarity_model_name='ItemCF_IUF', augmentation_warm_up_epoches=300, base_augment_type='random', augment_type_for_short='SIM', tao=0.2, gamma=0.7, beta=0.2, substitute_rate=0.1, insert_rate=0.4, max_insert_num_per_pos=1, temperature=1.0, n_views=2, model_name='CoSeRec', hidden_size=64, num_hidden_layers=2, num_attention_heads=2, hidden_act='gelu', attention_probs_dropout_prob=0.5, hidden_dropout_prob=0.5, initializer_range=0.02, max_seq_length=50, lr=0.001, batch_size=256, epochs=300, no_cuda=False, log_freq=1, seed=1, cf_weight=0.1, rec_weight=1.0, weight_decay=0.0, adam_beta1=0.9, adam_beta2=0.999, device_type='mps', cuda_condition=False, data_file='../data/mooc.txt', item_size=1304, mask_id=1303, log_file='output/CoSeRec-mooc-0.txt')
{'epoch': 0, 'rec_avg_loss': '1.0603', 'joint_avg_loss': '4.5332', 'cl_avg_loss': '34.7282', 'cl_pair_0_loss': '34.7282'}
{'Epoch': 0, 'HIT@5': '0.0661', 'NDCG@5': '0.0403', 'HIT@10': '0.1124', 'NDCG@10': '0.0551', 'HIT@20': '0.1995', 'NDCG@20': '0.0769'}
{'epoch': 1, 'rec_avg_loss': '0.8501', 'joint_avg_loss': '2.2230', 'cl_avg_loss': '13.7293', 'cl_pair_0_loss': '13.7293'}
{'Epoch': 1, 'HIT@5': '0.0874', 'NDCG@5': '0.0528', 'HIT@10': '0.1454', 'NDCG@10': '0.0716', 'HIT@20': '0.2169', 'NDCG@20': '0.0896'}
{'epoch': 2, 'rec_avg_loss': '0.7881', 'joint_avg_loss': '1.6196', 'cl_avg_loss': '8.3148', 'cl_pair_0_loss': '8.3148'}
{'Epoch': 2, 'HIT@5': '0.0941', 'NDCG@5': '0.0606', 'HIT@10': '0.1554', 'NDCG@10': '0.0804', 'HIT@20': '0.2275', 'NDCG@20': '0.0985'}
{'epoch': 3, 'rec_avg_loss': '0.7528', 'joint_avg_loss': '1.3901', 'cl_avg_loss': '6.3731', 'cl_pair_0_loss': '6.3731'}
{'Epoch': 3, 'HIT@5': '0.1159', 'NDCG@5': '0.0748', 'HIT@10': '0.1664', 'NDCG@10': '0.0913', 'HIT@20': '0.2323', 'NDCG@20': '0.1079'}
{'epoch': 4, 'rec_avg_loss': '0.7291', 'joint_avg_loss': '1.2680', 'cl_avg_loss': '5.3888', 'cl_pair_0_loss': '5.3888'}
{'Epoch': 4, 'HIT@5': '0.1225', 'NDCG@5': '0.0821', 'HIT@10': '0.1753', 'NDCG@10': '0.0994', 'HIT@20': '0.3458', 'NDCG@20': '0.1404'}
{'epoch': 5, 'rec_avg_loss': '0.7136', 'joint_avg_loss': '1.1889', 'cl_avg_loss': '4.7530', 'cl_pair_0_loss': '4.7530'}
{'Epoch': 5, 'HIT@5': '0.1346', 'NDCG@5': '0.0908', 'HIT@10': '0.1859', 'NDCG@10': '0.1073', 'HIT@20': '0.2555', 'NDCG@20': '0.1249'}
{'epoch': 6, 'rec_avg_loss': '0.7008', 'joint_avg_loss': '1.1346', 'cl_avg_loss': '4.3378', 'cl_pair_0_loss': '4.3378'}
{'Epoch': 6, 'HIT@5': '0.1398', 'NDCG@5': '0.0972', 'HIT@10': '0.1943', 'NDCG@10': '0.1147', 'HIT@20': '0.3561', 'NDCG@20': '0.1540'}
{'epoch': 7, 'rec_avg_loss': '0.6894', 'joint_avg_loss': '1.0964', 'cl_avg_loss': '4.0706', 'cl_pair_0_loss': '4.0706'}
{'Epoch': 7, 'HIT@5': '0.1419', 'NDCG@5': '0.0989', 'HIT@10': '0.1944', 'NDCG@10': '0.1159', 'HIT@20': '0.3582', 'NDCG@20': '0.1564'}
{'epoch': 8, 'rec_avg_loss': '0.6845', 'joint_avg_loss': '1.0641', 'cl_avg_loss': '3.7956', 'cl_pair_0_loss': '3.7956'}
{'Epoch': 8, 'HIT@5': '0.1399', 'NDCG@5': '0.0992', 'HIT@10': '0.1946', 'NDCG@10': '0.1168', 'HIT@20': '0.3607', 'NDCG@20': '0.1596'}
{'epoch': 9, 'rec_avg_loss': '0.6794', 'joint_avg_loss': '1.0450', 'cl_avg_loss': '3.6566', 'cl_pair_0_loss': '3.6566'}
{'Epoch': 9, 'HIT@5': '0.1417', 'NDCG@5': '0.1011', 'HIT@10': '0.1973', 'NDCG@10': '0.1191', 'HIT@20': '0.3640', 'NDCG@20': '0.1624'}
{'epoch': 10, 'rec_avg_loss': '0.6764', 'joint_avg_loss': '1.0297', 'cl_avg_loss': '3.5330', 'cl_pair_0_loss': '3.5330'}
{'Epoch': 10, 'HIT@5': '0.1475', 'NDCG@5': '0.1046', 'HIT@10': '0.2549', 'NDCG@10': '0.1379', 'HIT@20': '0.3663', 'NDCG@20': '0.1661'}
{'epoch': 11, 'rec_avg_loss': '0.6691', 'joint_avg_loss': '1.0150', 'cl_avg_loss': '3.4598', 'cl_pair_0_loss': '3.4598'}
{'Epoch': 11, 'HIT@5': '0.1475', 'NDCG@5': '0.1047', 'HIT@10': '0.2608', 'NDCG@10': '0.1412', 'HIT@20': '0.3713', 'NDCG@20': '0.1693'}
{'epoch': 12, 'rec_avg_loss': '0.6673', 'joint_avg_loss': '1.0036', 'cl_avg_loss': '3.3627', 'cl_pair_0_loss': '3.3627'}
{'Epoch': 12, 'HIT@5': '0.1484', 'NDCG@5': '0.1074', 'HIT@10': '0.2653', 'NDCG@10': '0.1436', 'HIT@20': '0.3782', 'NDCG@20': '0.1720'}
{'epoch': 13, 'rec_avg_loss': '0.6621', 'joint_avg_loss': '0.9891', 'cl_avg_loss': '3.2705', 'cl_pair_0_loss': '3.2705'}
{'Epoch': 13, 'HIT@5': '0.1503', 'NDCG@5': '0.1087', 'HIT@10': '0.2625', 'NDCG@10': '0.1441', 'HIT@20': '0.3816', 'NDCG@20': '0.1744'}
{'epoch': 14, 'rec_avg_loss': '0.6552', 'joint_avg_loss': '0.9783', 'cl_avg_loss': '3.2305', 'cl_pair_0_loss': '3.2305'}
{'Epoch': 14, 'HIT@5': '0.1512', 'NDCG@5': '0.1110', 'HIT@10': '0.2656', 'NDCG@10': '0.1473', 'HIT@20': '0.3901', 'NDCG@20': '0.1790'}
{'epoch': 15, 'rec_avg_loss': '0.6514', 'joint_avg_loss': '0.9677', 'cl_avg_loss': '3.1632', 'cl_pair_0_loss': '3.1632'}
{'Epoch': 15, 'HIT@5': '0.1564', 'NDCG@5': '0.1108', 'HIT@10': '0.2709', 'NDCG@10': '0.1473', 'HIT@20': '0.3966', 'NDCG@20': '0.1799'}
{'epoch': 16, 'rec_avg_loss': '0.6482', 'joint_avg_loss': '0.9618', 'cl_avg_loss': '3.1358', 'cl_pair_0_loss': '3.1358'}
{'Epoch': 16, 'HIT@5': '0.1563', 'NDCG@5': '0.1120', 'HIT@10': '0.2663', 'NDCG@10': '0.1467', 'HIT@20': '0.3937', 'NDCG@20': '0.1792'}
{'epoch': 17, 'rec_avg_loss': '0.6458', 'joint_avg_loss': '0.9572', 'cl_avg_loss': '3.1138', 'cl_pair_0_loss': '3.1138'}
{'Epoch': 17, 'HIT@5': '0.1618', 'NDCG@5': '0.1145', 'HIT@10': '0.2711', 'NDCG@10': '0.1493', 'HIT@20': '0.4083', 'NDCG@20': '0.1843'}
{'epoch': 18, 'rec_avg_loss': '0.6389', 'joint_avg_loss': '0.9473', 'cl_avg_loss': '3.0836', 'cl_pair_0_loss': '3.0836'}
{'Epoch': 18, 'HIT@5': '0.1602', 'NDCG@5': '0.1158', 'HIT@10': '0.2768', 'NDCG@10': '0.1525', 'HIT@20': '0.4143', 'NDCG@20': '0.1881'}
{'epoch': 19, 'rec_avg_loss': '0.6357', 'joint_avg_loss': '0.9400', 'cl_avg_loss': '3.0435', 'cl_pair_0_loss': '3.0435'}
{'Epoch': 19, 'HIT@5': '0.1651', 'NDCG@5': '0.1203', 'HIT@10': '0.2812', 'NDCG@10': '0.1571', 'HIT@20': '0.4171', 'NDCG@20': '0.1918'}
{'epoch': 20, 'rec_avg_loss': '0.6295', 'joint_avg_loss': '0.9298', 'cl_avg_loss': '3.0023', 'cl_pair_0_loss': '3.0023'}
{'Epoch': 20, 'HIT@5': '0.2193', 'NDCG@5': '0.1417', 'HIT@10': '0.3273', 'NDCG@10': '0.1755', 'HIT@20': '0.4267', 'NDCG@20': '0.2004'}
{'epoch': 21, 'rec_avg_loss': '0.6251', 'joint_avg_loss': '0.9262', 'cl_avg_loss': '3.0103', 'cl_pair_0_loss': '3.0103'}
{'Epoch': 21, 'HIT@5': '0.2243', 'NDCG@5': '0.1462', 'HIT@10': '0.3315', 'NDCG@10': '0.1810', 'HIT@20': '0.4296', 'NDCG@20': '0.2055'}
{'epoch': 22, 'rec_avg_loss': '0.6180', 'joint_avg_loss': '0.9166', 'cl_avg_loss': '2.9859', 'cl_pair_0_loss': '2.9859'}
{'Epoch': 22, 'HIT@5': '0.2300', 'NDCG@5': '0.1523', 'HIT@10': '0.3372', 'NDCG@10': '0.1873', 'HIT@20': '0.4403', 'NDCG@20': '0.2133'}
{'epoch': 23, 'rec_avg_loss': '0.6119', 'joint_avg_loss': '0.9126', 'cl_avg_loss': '3.0072', 'cl_pair_0_loss': '3.0072'}
{'Epoch': 23, 'HIT@5': '0.2713', 'NDCG@5': '0.1781', 'HIT@10': '0.3416', 'NDCG@10': '0.2010', 'HIT@20': '0.4444', 'NDCG@20': '0.2269'}
{'epoch': 24, 'rec_avg_loss': '0.6074', 'joint_avg_loss': '0.9033', 'cl_avg_loss': '2.9597', 'cl_pair_0_loss': '2.9597'}
{'Epoch': 24, 'HIT@5': '0.2377', 'NDCG@5': '0.1627', 'HIT@10': '0.3482', 'NDCG@10': '0.1996', 'HIT@20': '0.4492', 'NDCG@20': '0.2251'}
{'epoch': 25, 'rec_avg_loss': '0.6023', 'joint_avg_loss': '0.8993', 'cl_avg_loss': '2.9701', 'cl_pair_0_loss': '2.9701'}
{'Epoch': 25, 'HIT@5': '0.2721', 'NDCG@5': '0.1737', 'HIT@10': '0.3459', 'NDCG@10': '0.1976', 'HIT@20': '0.4539', 'NDCG@20': '0.2248'}
{'epoch': 26, 'rec_avg_loss': '0.5939', 'joint_avg_loss': '0.8890', 'cl_avg_loss': '2.9519', 'cl_pair_0_loss': '2.9519'}
{'Epoch': 26, 'HIT@5': '0.2864', 'NDCG@5': '0.1876', 'HIT@10': '0.3577', 'NDCG@10': '0.2106', 'HIT@20': '0.4621', 'NDCG@20': '0.2371'}
{'epoch': 27, 'rec_avg_loss': '0.5913', 'joint_avg_loss': '0.8858', 'cl_avg_loss': '2.9446', 'cl_pair_0_loss': '2.9446'}
{'Epoch': 27, 'HIT@5': '0.2902', 'NDCG@5': '0.1899', 'HIT@10': '0.3645', 'NDCG@10': '0.2138', 'HIT@20': '0.4635', 'NDCG@20': '0.2388'}
{'epoch': 28, 'rec_avg_loss': '0.5847', 'joint_avg_loss': '0.8798', 'cl_avg_loss': '2.9512', 'cl_pair_0_loss': '2.9512'}
{'Epoch': 28, 'HIT@5': '0.2928', 'NDCG@5': '0.1967', 'HIT@10': '0.3708', 'NDCG@10': '0.2218', 'HIT@20': '0.4732', 'NDCG@20': '0.2477'}
{'epoch': 29, 'rec_avg_loss': '0.5825', 'joint_avg_loss': '0.8768', 'cl_avg_loss': '2.9430', 'cl_pair_0_loss': '2.9430'}
{'Epoch': 29, 'HIT@5': '0.3015', 'NDCG@5': '0.1996', 'HIT@10': '0.3786', 'NDCG@10': '0.2243', 'HIT@20': '0.4759', 'NDCG@20': '0.2490'}
{'epoch': 30, 'rec_avg_loss': '0.5761', 'joint_avg_loss': '0.8710', 'cl_avg_loss': '2.9492', 'cl_pair_0_loss': '2.9492'}
{'Epoch': 30, 'HIT@5': '0.3037', 'NDCG@5': '0.2097', 'HIT@10': '0.3799', 'NDCG@10': '0.2341', 'HIT@20': '0.4776', 'NDCG@20': '0.2588'}
{'epoch': 31, 'rec_avg_loss': '0.5722', 'joint_avg_loss': '0.8636', 'cl_avg_loss': '2.9142', 'cl_pair_0_loss': '2.9142'}
{'Epoch': 31, 'HIT@5': '0.3099', 'NDCG@5': '0.2154', 'HIT@10': '0.3871', 'NDCG@10': '0.2402', 'HIT@20': '0.4820', 'NDCG@20': '0.2641'}
{'epoch': 32, 'rec_avg_loss': '0.5657', 'joint_avg_loss': '0.8575', 'cl_avg_loss': '2.9186', 'cl_pair_0_loss': '2.9186'}
{'Epoch': 32, 'HIT@5': '0.3107', 'NDCG@5': '0.2163', 'HIT@10': '0.3918', 'NDCG@10': '0.2424', 'HIT@20': '0.4834', 'NDCG@20': '0.2655'}
{'epoch': 33, 'rec_avg_loss': '0.5655', 'joint_avg_loss': '0.8574', 'cl_avg_loss': '2.9185', 'cl_pair_0_loss': '2.9185'}
{'Epoch': 33, 'HIT@5': '0.3153', 'NDCG@5': '0.2194', 'HIT@10': '0.3975', 'NDCG@10': '0.2458', 'HIT@20': '0.4882', 'NDCG@20': '0.2687'}
{'epoch': 34, 'rec_avg_loss': '0.5587', 'joint_avg_loss': '0.8477', 'cl_avg_loss': '2.8897', 'cl_pair_0_loss': '2.8897'}
{'Epoch': 34, 'HIT@5': '0.3162', 'NDCG@5': '0.2189', 'HIT@10': '0.3967', 'NDCG@10': '0.2448', 'HIT@20': '0.4911', 'NDCG@20': '0.2688'}
{'epoch': 35, 'rec_avg_loss': '0.5534', 'joint_avg_loss': '0.8457', 'cl_avg_loss': '2.9226', 'cl_pair_0_loss': '2.9226'}
{'Epoch': 35, 'HIT@5': '0.3227', 'NDCG@5': '0.2257', 'HIT@10': '0.4011', 'NDCG@10': '0.2512', 'HIT@20': '0.4955', 'NDCG@20': '0.2750'}
{'epoch': 36, 'rec_avg_loss': '0.5488', 'joint_avg_loss': '0.8373', 'cl_avg_loss': '2.8844', 'cl_pair_0_loss': '2.8844'}
{'Epoch': 36, 'HIT@5': '0.3296', 'NDCG@5': '0.2283', 'HIT@10': '0.4105', 'NDCG@10': '0.2545', 'HIT@20': '0.5048', 'NDCG@20': '0.2784'}
{'epoch': 37, 'rec_avg_loss': '0.5429', 'joint_avg_loss': '0.8336', 'cl_avg_loss': '2.9066', 'cl_pair_0_loss': '2.9066'}
{'Epoch': 37, 'HIT@5': '0.3306', 'NDCG@5': '0.2292', 'HIT@10': '0.4118', 'NDCG@10': '0.2556', 'HIT@20': '0.5022', 'NDCG@20': '0.2783'}
{'epoch': 38, 'rec_avg_loss': '0.5411', 'joint_avg_loss': '0.8309', 'cl_avg_loss': '2.8976', 'cl_pair_0_loss': '2.8976'}
{'Epoch': 38, 'HIT@5': '0.3336', 'NDCG@5': '0.2334', 'HIT@10': '0.4119', 'NDCG@10': '0.2587', 'HIT@20': '0.5053', 'NDCG@20': '0.2823'}
{'epoch': 39, 'rec_avg_loss': '0.5373', 'joint_avg_loss': '0.8274', 'cl_avg_loss': '2.9010', 'cl_pair_0_loss': '2.9010'}
{'Epoch': 39, 'HIT@5': '0.3296', 'NDCG@5': '0.2285', 'HIT@10': '0.4122', 'NDCG@10': '0.2553', 'HIT@20': '0.5061', 'NDCG@20': '0.2790'}
{'epoch': 40, 'rec_avg_loss': '0.5331', 'joint_avg_loss': '0.8203', 'cl_avg_loss': '2.8724', 'cl_pair_0_loss': '2.8724'}
{'Epoch': 40, 'HIT@5': '0.3378', 'NDCG@5': '0.2349', 'HIT@10': '0.4157', 'NDCG@10': '0.2600', 'HIT@20': '0.5103', 'NDCG@20': '0.2838'}
{'epoch': 41, 'rec_avg_loss': '0.5277', 'joint_avg_loss': '0.8171', 'cl_avg_loss': '2.8945', 'cl_pair_0_loss': '2.8945'}
{'Epoch': 41, 'HIT@5': '0.3404', 'NDCG@5': '0.2369', 'HIT@10': '0.4153', 'NDCG@10': '0.2610', 'HIT@20': '0.5105', 'NDCG@20': '0.2850'}
{'epoch': 42, 'rec_avg_loss': '0.5250', 'joint_avg_loss': '0.8142', 'cl_avg_loss': '2.8926', 'cl_pair_0_loss': '2.8926'}
{'Epoch': 42, 'HIT@5': '0.3447', 'NDCG@5': '0.2397', 'HIT@10': '0.4209', 'NDCG@10': '0.2643', 'HIT@20': '0.5150', 'NDCG@20': '0.2880'}
{'epoch': 43, 'rec_avg_loss': '0.5220', 'joint_avg_loss': '0.8103', 'cl_avg_loss': '2.8833', 'cl_pair_0_loss': '2.8833'}
{'Epoch': 43, 'HIT@5': '0.3468', 'NDCG@5': '0.2418', 'HIT@10': '0.4226', 'NDCG@10': '0.2663', 'HIT@20': '0.5172', 'NDCG@20': '0.2902'}
{'epoch': 44, 'rec_avg_loss': '0.5197', 'joint_avg_loss': '0.8041', 'cl_avg_loss': '2.8449', 'cl_pair_0_loss': '2.8449'}
{'Epoch': 44, 'HIT@5': '0.3460', 'NDCG@5': '0.2409', 'HIT@10': '0.4210', 'NDCG@10': '0.2650', 'HIT@20': '0.5153', 'NDCG@20': '0.2888'}
{'epoch': 45, 'rec_avg_loss': '0.5119', 'joint_avg_loss': '0.7985', 'cl_avg_loss': '2.8658', 'cl_pair_0_loss': '2.8658'}
{'Epoch': 45, 'HIT@5': '0.3437', 'NDCG@5': '0.2391', 'HIT@10': '0.4201', 'NDCG@10': '0.2637', 'HIT@20': '0.5175', 'NDCG@20': '0.2883'}
{'epoch': 46, 'rec_avg_loss': '0.5103', 'joint_avg_loss': '0.7951', 'cl_avg_loss': '2.8487', 'cl_pair_0_loss': '2.8487'}
{'Epoch': 46, 'HIT@5': '0.3432', 'NDCG@5': '0.2380', 'HIT@10': '0.4210', 'NDCG@10': '0.2632', 'HIT@20': '0.5182', 'NDCG@20': '0.2877'}
{'epoch': 47, 'rec_avg_loss': '0.5068', 'joint_avg_loss': '0.7905', 'cl_avg_loss': '2.8371', 'cl_pair_0_loss': '2.8371'}
{'Epoch': 47, 'HIT@5': '0.3406', 'NDCG@5': '0.2367', 'HIT@10': '0.4229', 'NDCG@10': '0.2632', 'HIT@20': '0.5219', 'NDCG@20': '0.2883'}
{'epoch': 48, 'rec_avg_loss': '0.5055', 'joint_avg_loss': '0.7920', 'cl_avg_loss': '2.8645', 'cl_pair_0_loss': '2.8645'}
{'Epoch': 48, 'HIT@5': '0.3456', 'NDCG@5': '0.2410', 'HIT@10': '0.4270', 'NDCG@10': '0.2672', 'HIT@20': '0.5240', 'NDCG@20': '0.2916'}
{'epoch': 49, 'rec_avg_loss': '0.5002', 'joint_avg_loss': '0.7839', 'cl_avg_loss': '2.8368', 'cl_pair_0_loss': '2.8368'}
{'Epoch': 49, 'HIT@5': '0.3471', 'NDCG@5': '0.2443', 'HIT@10': '0.4256', 'NDCG@10': '0.2695', 'HIT@20': '0.5246', 'NDCG@20': '0.2945'}
{'epoch': 50, 'rec_avg_loss': '0.4985', 'joint_avg_loss': '0.7804', 'cl_avg_loss': '2.8196', 'cl_pair_0_loss': '2.8196'}
{'Epoch': 50, 'HIT@5': '0.3487', 'NDCG@5': '0.2436', 'HIT@10': '0.4259', 'NDCG@10': '0.2685', 'HIT@20': '0.5246', 'NDCG@20': '0.2933'}
{'epoch': 51, 'rec_avg_loss': '0.4958', 'joint_avg_loss': '0.7768', 'cl_avg_loss': '2.8099', 'cl_pair_0_loss': '2.8099'}
{'Epoch': 51, 'HIT@5': '0.3481', 'NDCG@5': '0.2438', 'HIT@10': '0.4286', 'NDCG@10': '0.2698', 'HIT@20': '0.5244', 'NDCG@20': '0.2940'}
{'epoch': 52, 'rec_avg_loss': '0.4942', 'joint_avg_loss': '0.7776', 'cl_avg_loss': '2.8343', 'cl_pair_0_loss': '2.8343'}
{'Epoch': 52, 'HIT@5': '0.3482', 'NDCG@5': '0.2443', 'HIT@10': '0.4291', 'NDCG@10': '0.2705', 'HIT@20': '0.5254', 'NDCG@20': '0.2948'}
{'epoch': 53, 'rec_avg_loss': '0.4909', 'joint_avg_loss': '0.7740', 'cl_avg_loss': '2.8308', 'cl_pair_0_loss': '2.8308'}
{'Epoch': 53, 'HIT@5': '0.3498', 'NDCG@5': '0.2427', 'HIT@10': '0.4285', 'NDCG@10': '0.2681', 'HIT@20': '0.5241', 'NDCG@20': '0.2923'}
{'epoch': 54, 'rec_avg_loss': '0.4925', 'joint_avg_loss': '0.7730', 'cl_avg_loss': '2.8055', 'cl_pair_0_loss': '2.8055'}
{'Epoch': 54, 'HIT@5': '0.3494', 'NDCG@5': '0.2464', 'HIT@10': '0.4312', 'NDCG@10': '0.2726', 'HIT@20': '0.5216', 'NDCG@20': '0.2954'}
{'epoch': 55, 'rec_avg_loss': '0.4873', 'joint_avg_loss': '0.7680', 'cl_avg_loss': '2.8069', 'cl_pair_0_loss': '2.8069'}
{'Epoch': 55, 'HIT@5': '0.3493', 'NDCG@5': '0.2450', 'HIT@10': '0.4329', 'NDCG@10': '0.2718', 'HIT@20': '0.5237', 'NDCG@20': '0.2947'}
{'epoch': 56, 'rec_avg_loss': '0.4856', 'joint_avg_loss': '0.7659', 'cl_avg_loss': '2.8031', 'cl_pair_0_loss': '2.8031'}
{'Epoch': 56, 'HIT@5': '0.3510', 'NDCG@5': '0.2453', 'HIT@10': '0.4352', 'NDCG@10': '0.2724', 'HIT@20': '0.5263', 'NDCG@20': '0.2953'}
{'epoch': 57, 'rec_avg_loss': '0.4848', 'joint_avg_loss': '0.7625', 'cl_avg_loss': '2.7777', 'cl_pair_0_loss': '2.7777'}
{'Epoch': 57, 'HIT@5': '0.3530', 'NDCG@5': '0.2477', 'HIT@10': '0.4353', 'NDCG@10': '0.2741', 'HIT@20': '0.5252', 'NDCG@20': '0.2967'}
{'epoch': 58, 'rec_avg_loss': '0.4812', 'joint_avg_loss': '0.7582', 'cl_avg_loss': '2.7695', 'cl_pair_0_loss': '2.7695'}
{'Epoch': 58, 'HIT@5': '0.3519', 'NDCG@5': '0.2476', 'HIT@10': '0.4350', 'NDCG@10': '0.2743', 'HIT@20': '0.5270', 'NDCG@20': '0.2975'}
{'epoch': 59, 'rec_avg_loss': '0.4803', 'joint_avg_loss': '0.7583', 'cl_avg_loss': '2.7806', 'cl_pair_0_loss': '2.7806'}
{'Epoch': 59, 'HIT@5': '0.3522', 'NDCG@5': '0.2467', 'HIT@10': '0.4353', 'NDCG@10': '0.2734', 'HIT@20': '0.5267', 'NDCG@20': '0.2964'}
{'epoch': 60, 'rec_avg_loss': '0.4793', 'joint_avg_loss': '0.7569', 'cl_avg_loss': '2.7762', 'cl_pair_0_loss': '2.7762'}
{'Epoch': 60, 'HIT@5': '0.3533', 'NDCG@5': '0.2476', 'HIT@10': '0.4352', 'NDCG@10': '0.2739', 'HIT@20': '0.5282', 'NDCG@20': '0.2973'}
{'epoch': 61, 'rec_avg_loss': '0.4758', 'joint_avg_loss': '0.7511', 'cl_avg_loss': '2.7533', 'cl_pair_0_loss': '2.7533'}
{'Epoch': 61, 'HIT@5': '0.3535', 'NDCG@5': '0.2480', 'HIT@10': '0.4343', 'NDCG@10': '0.2739', 'HIT@20': '0.5270', 'NDCG@20': '0.2972'}
{'epoch': 62, 'rec_avg_loss': '0.4758', 'joint_avg_loss': '0.7515', 'cl_avg_loss': '2.7573', 'cl_pair_0_loss': '2.7573'}
{'Epoch': 62, 'HIT@5': '0.3534', 'NDCG@5': '0.2468', 'HIT@10': '0.4353', 'NDCG@10': '0.2732', 'HIT@20': '0.5278', 'NDCG@20': '0.2965'}
{'epoch': 63, 'rec_avg_loss': '0.4754', 'joint_avg_loss': '0.7500', 'cl_avg_loss': '2.7461', 'cl_pair_0_loss': '2.7461'}
{'Epoch': 63, 'HIT@5': '0.3525', 'NDCG@5': '0.2468', 'HIT@10': '0.4327', 'NDCG@10': '0.2727', 'HIT@20': '0.5296', 'NDCG@20': '0.2970'}
{'epoch': 64, 'rec_avg_loss': '0.4716', 'joint_avg_loss': '0.7458', 'cl_avg_loss': '2.7422', 'cl_pair_0_loss': '2.7422'}
{'Epoch': 64, 'HIT@5': '0.3542', 'NDCG@5': '0.2470', 'HIT@10': '0.4345', 'NDCG@10': '0.2729', 'HIT@20': '0.5289', 'NDCG@20': '0.2966'}
{'epoch': 65, 'rec_avg_loss': '0.4703', 'joint_avg_loss': '0.7428', 'cl_avg_loss': '2.7252', 'cl_pair_0_loss': '2.7252'}
{'Epoch': 65, 'HIT@5': '0.3520', 'NDCG@5': '0.2465', 'HIT@10': '0.4316', 'NDCG@10': '0.2722', 'HIT@20': '0.5296', 'NDCG@20': '0.2968'}
{'epoch': 66, 'rec_avg_loss': '0.4709', 'joint_avg_loss': '0.7420', 'cl_avg_loss': '2.7114', 'cl_pair_0_loss': '2.7114'}
{'Epoch': 66, 'HIT@5': '0.3515', 'NDCG@5': '0.2463', 'HIT@10': '0.4339', 'NDCG@10': '0.2729', 'HIT@20': '0.5301', 'NDCG@20': '0.2971'}
{'epoch': 67, 'rec_avg_loss': '0.4655', 'joint_avg_loss': '0.7358', 'cl_avg_loss': '2.7032', 'cl_pair_0_loss': '2.7032'}
{'Epoch': 67, 'HIT@5': '0.3497', 'NDCG@5': '0.2431', 'HIT@10': '0.4329', 'NDCG@10': '0.2698', 'HIT@20': '0.5313', 'NDCG@20': '0.2944'}
{'epoch': 68, 'rec_avg_loss': '0.4656', 'joint_avg_loss': '0.7378', 'cl_avg_loss': '2.7221', 'cl_pair_0_loss': '2.7221'}
{'Epoch': 68, 'HIT@5': '0.3502', 'NDCG@5': '0.2455', 'HIT@10': '0.4324', 'NDCG@10': '0.2721', 'HIT@20': '0.5328', 'NDCG@20': '0.2973'}
{'epoch': 69, 'rec_avg_loss': '0.4651', 'joint_avg_loss': '0.7383', 'cl_avg_loss': '2.7326', 'cl_pair_0_loss': '2.7326'}
{'Epoch': 69, 'HIT@5': '0.3509', 'NDCG@5': '0.2467', 'HIT@10': '0.4303', 'NDCG@10': '0.2724', 'HIT@20': '0.5312', 'NDCG@20': '0.2978'}
{'epoch': 70, 'rec_avg_loss': '0.4660', 'joint_avg_loss': '0.7366', 'cl_avg_loss': '2.7063', 'cl_pair_0_loss': '2.7063'}
{'Epoch': 70, 'HIT@5': '0.3497', 'NDCG@5': '0.2456', 'HIT@10': '0.4312', 'NDCG@10': '0.2719', 'HIT@20': '0.5325', 'NDCG@20': '0.2974'}
{'epoch': 71, 'rec_avg_loss': '0.4656', 'joint_avg_loss': '0.7374', 'cl_avg_loss': '2.7183', 'cl_pair_0_loss': '2.7183'}
{'Epoch': 71, 'HIT@5': '0.3501', 'NDCG@5': '0.2470', 'HIT@10': '0.4326', 'NDCG@10': '0.2735', 'HIT@20': '0.5317', 'NDCG@20': '0.2985'}
{'epoch': 72, 'rec_avg_loss': '0.4633', 'joint_avg_loss': '0.7321', 'cl_avg_loss': '2.6882', 'cl_pair_0_loss': '2.6882'}
{'Epoch': 72, 'HIT@5': '0.3529', 'NDCG@5': '0.2441', 'HIT@10': '0.4316', 'NDCG@10': '0.2695', 'HIT@20': '0.5343', 'NDCG@20': '0.2954'}
{'epoch': 73, 'rec_avg_loss': '0.4628', 'joint_avg_loss': '0.7327', 'cl_avg_loss': '2.6987', 'cl_pair_0_loss': '2.6987'}
{'Epoch': 73, 'HIT@5': '0.3518', 'NDCG@5': '0.2457', 'HIT@10': '0.4318', 'NDCG@10': '0.2714', 'HIT@20': '0.5319', 'NDCG@20': '0.2965'}
{'epoch': 74, 'rec_avg_loss': '0.4625', 'joint_avg_loss': '0.7322', 'cl_avg_loss': '2.6966', 'cl_pair_0_loss': '2.6966'}
{'Epoch': 74, 'HIT@5': '0.3551', 'NDCG@5': '0.2474', 'HIT@10': '0.4327', 'NDCG@10': '0.2724', 'HIT@20': '0.5329', 'NDCG@20': '0.2975'}
{'epoch': 75, 'rec_avg_loss': '0.4585', 'joint_avg_loss': '0.7277', 'cl_avg_loss': '2.6918', 'cl_pair_0_loss': '2.6918'}
{'Epoch': 75, 'HIT@5': '0.3527', 'NDCG@5': '0.2472', 'HIT@10': '0.4340', 'NDCG@10': '0.2736', 'HIT@20': '0.5343', 'NDCG@20': '0.2988'}
{'epoch': 76, 'rec_avg_loss': '0.4586', 'joint_avg_loss': '0.7271', 'cl_avg_loss': '2.6852', 'cl_pair_0_loss': '2.6852'}
{'Epoch': 76, 'HIT@5': '0.3477', 'NDCG@5': '0.2445', 'HIT@10': '0.4328', 'NDCG@10': '0.2720', 'HIT@20': '0.5348', 'NDCG@20': '0.2977'}
{'epoch': 77, 'rec_avg_loss': '0.4602', 'joint_avg_loss': '0.7278', 'cl_avg_loss': '2.6762', 'cl_pair_0_loss': '2.6762'}
{'Epoch': 77, 'HIT@5': '0.3554', 'NDCG@5': '0.2470', 'HIT@10': '0.4335', 'NDCG@10': '0.2721', 'HIT@20': '0.5363', 'NDCG@20': '0.2980'}
{'epoch': 78, 'rec_avg_loss': '0.4573', 'joint_avg_loss': '0.7224', 'cl_avg_loss': '2.6511', 'cl_pair_0_loss': '2.6511'}
{'Epoch': 78, 'HIT@5': '0.3480', 'NDCG@5': '0.2437', 'HIT@10': '0.4313', 'NDCG@10': '0.2707', 'HIT@20': '0.5359', 'NDCG@20': '0.2971'}
{'epoch': 79, 'rec_avg_loss': '0.4580', 'joint_avg_loss': '0.7260', 'cl_avg_loss': '2.6792', 'cl_pair_0_loss': '2.6792'}
{'Epoch': 79, 'HIT@5': '0.3532', 'NDCG@5': '0.2461', 'HIT@10': '0.4330', 'NDCG@10': '0.2718', 'HIT@20': '0.5372', 'NDCG@20': '0.2980'}
{'epoch': 80, 'rec_avg_loss': '0.4550', 'joint_avg_loss': '0.7230', 'cl_avg_loss': '2.6800', 'cl_pair_0_loss': '2.6800'}
{'Epoch': 80, 'HIT@5': '0.3466', 'NDCG@5': '0.2442', 'HIT@10': '0.4321', 'NDCG@10': '0.2718', 'HIT@20': '0.5355', 'NDCG@20': '0.2978'}
{'epoch': 81, 'rec_avg_loss': '0.4548', 'joint_avg_loss': '0.7197', 'cl_avg_loss': '2.6494', 'cl_pair_0_loss': '2.6494'}
{'Epoch': 81, 'HIT@5': '0.3511', 'NDCG@5': '0.2441', 'HIT@10': '0.4325', 'NDCG@10': '0.2704', 'HIT@20': '0.5372', 'NDCG@20': '0.2967'}
{'epoch': 82, 'rec_avg_loss': '0.4568', 'joint_avg_loss': '0.7212', 'cl_avg_loss': '2.6443', 'cl_pair_0_loss': '2.6443'}
{'Epoch': 82, 'HIT@5': '0.3511', 'NDCG@5': '0.2450', 'HIT@10': '0.4340', 'NDCG@10': '0.2717', 'HIT@20': '0.5362', 'NDCG@20': '0.2974'}
{'epoch': 83, 'rec_avg_loss': '0.4543', 'joint_avg_loss': '0.7195', 'cl_avg_loss': '2.6522', 'cl_pair_0_loss': '2.6522'}
{'Epoch': 83, 'HIT@5': '0.3500', 'NDCG@5': '0.2426', 'HIT@10': '0.4342', 'NDCG@10': '0.2697', 'HIT@20': '0.5378', 'NDCG@20': '0.2957'}
{'epoch': 84, 'rec_avg_loss': '0.4509', 'joint_avg_loss': '0.7155', 'cl_avg_loss': '2.6467', 'cl_pair_0_loss': '2.6467'}
{'Epoch': 84, 'HIT@5': '0.3501', 'NDCG@5': '0.2429', 'HIT@10': '0.4339', 'NDCG@10': '0.2699', 'HIT@20': '0.5350', 'NDCG@20': '0.2954'}
{'epoch': 85, 'rec_avg_loss': '0.4513', 'joint_avg_loss': '0.7171', 'cl_avg_loss': '2.6576', 'cl_pair_0_loss': '2.6576'}
{'Epoch': 85, 'HIT@5': '0.3511', 'NDCG@5': '0.2409', 'HIT@10': '0.4341', 'NDCG@10': '0.2676', 'HIT@20': '0.5370', 'NDCG@20': '0.2935'}
{'epoch': 86, 'rec_avg_loss': '0.4529', 'joint_avg_loss': '0.7164', 'cl_avg_loss': '2.6352', 'cl_pair_0_loss': '2.6352'}
{'Epoch': 86, 'HIT@5': '0.3516', 'NDCG@5': '0.2429', 'HIT@10': '0.4350', 'NDCG@10': '0.2698', 'HIT@20': '0.5384', 'NDCG@20': '0.2958'}
{'epoch': 87, 'rec_avg_loss': '0.4503', 'joint_avg_loss': '0.7138', 'cl_avg_loss': '2.6347', 'cl_pair_0_loss': '2.6347'}
{'Epoch': 87, 'HIT@5': '0.3475', 'NDCG@5': '0.2411', 'HIT@10': '0.4330', 'NDCG@10': '0.2686', 'HIT@20': '0.5371', 'NDCG@20': '0.2949'}
{'epoch': 88, 'rec_avg_loss': '0.4505', 'joint_avg_loss': '0.7145', 'cl_avg_loss': '2.6400', 'cl_pair_0_loss': '2.6400'}
{'Epoch': 88, 'HIT@5': '0.3481', 'NDCG@5': '0.2409', 'HIT@10': '0.4349', 'NDCG@10': '0.2688', 'HIT@20': '0.5361', 'NDCG@20': '0.2943'}
{'epoch': 89, 'rec_avg_loss': '0.4485', 'joint_avg_loss': '0.7100', 'cl_avg_loss': '2.6154', 'cl_pair_0_loss': '2.6154'}
{'Epoch': 89, 'HIT@5': '0.3484', 'NDCG@5': '0.2414', 'HIT@10': '0.4344', 'NDCG@10': '0.2690', 'HIT@20': '0.5382', 'NDCG@20': '0.2952'}
{'epoch': 90, 'rec_avg_loss': '0.4480', 'joint_avg_loss': '0.7120', 'cl_avg_loss': '2.6399', 'cl_pair_0_loss': '2.6399'}
{'Epoch': 90, 'HIT@5': '0.3479', 'NDCG@5': '0.2412', 'HIT@10': '0.4355', 'NDCG@10': '0.2693', 'HIT@20': '0.5377', 'NDCG@20': '0.2951'}
{'epoch': 91, 'rec_avg_loss': '0.4490', 'joint_avg_loss': '0.7125', 'cl_avg_loss': '2.6347', 'cl_pair_0_loss': '2.6347'}
{'Epoch': 91, 'HIT@5': '0.3496', 'NDCG@5': '0.2421', 'HIT@10': '0.4352', 'NDCG@10': '0.2697', 'HIT@20': '0.5391', 'NDCG@20': '0.2959'}
{'epoch': 92, 'rec_avg_loss': '0.4489', 'joint_avg_loss': '0.7078', 'cl_avg_loss': '2.5890', 'cl_pair_0_loss': '2.5890'}
{'Epoch': 92, 'HIT@5': '0.3464', 'NDCG@5': '0.2395', 'HIT@10': '0.4338', 'NDCG@10': '0.2677', 'HIT@20': '0.5365', 'NDCG@20': '0.2936'}
{'epoch': 93, 'rec_avg_loss': '0.4439', 'joint_avg_loss': '0.7061', 'cl_avg_loss': '2.6216', 'cl_pair_0_loss': '2.6216'}
{'Epoch': 93, 'HIT@5': '0.3496', 'NDCG@5': '0.2427', 'HIT@10': '0.4367', 'NDCG@10': '0.2708', 'HIT@20': '0.5412', 'NDCG@20': '0.2971'}
{'epoch': 94, 'rec_avg_loss': '0.4469', 'joint_avg_loss': '0.7074', 'cl_avg_loss': '2.6056', 'cl_pair_0_loss': '2.6056'}
{'Epoch': 94, 'HIT@5': '0.3484', 'NDCG@5': '0.2420', 'HIT@10': '0.4349', 'NDCG@10': '0.2698', 'HIT@20': '0.5403', 'NDCG@20': '0.2964'}
{'epoch': 95, 'rec_avg_loss': '0.4489', 'joint_avg_loss': '0.7081', 'cl_avg_loss': '2.5921', 'cl_pair_0_loss': '2.5921'}
{'Epoch': 95, 'HIT@5': '0.3456', 'NDCG@5': '0.2378', 'HIT@10': '0.4337', 'NDCG@10': '0.2661', 'HIT@20': '0.5381', 'NDCG@20': '0.2925'}
{'epoch': 96, 'rec_avg_loss': '0.4458', 'joint_avg_loss': '0.7054', 'cl_avg_loss': '2.5961', 'cl_pair_0_loss': '2.5961'}
{'Epoch': 96, 'HIT@5': '0.3475', 'NDCG@5': '0.2389', 'HIT@10': '0.4361', 'NDCG@10': '0.2674', 'HIT@20': '0.5397', 'NDCG@20': '0.2935'}
{'epoch': 97, 'rec_avg_loss': '0.4472', 'joint_avg_loss': '0.7073', 'cl_avg_loss': '2.6003', 'cl_pair_0_loss': '2.6003'}
{'Epoch': 97, 'HIT@5': '0.3504', 'NDCG@5': '0.2446', 'HIT@10': '0.4378', 'NDCG@10': '0.2727', 'HIT@20': '0.5417', 'NDCG@20': '0.2989'}
{'epoch': 98, 'rec_avg_loss': '0.4426', 'joint_avg_loss': '0.7034', 'cl_avg_loss': '2.6080', 'cl_pair_0_loss': '2.6080'}
{'Epoch': 98, 'HIT@5': '0.3485', 'NDCG@5': '0.2391', 'HIT@10': '0.4369', 'NDCG@10': '0.2677', 'HIT@20': '0.5398', 'NDCG@20': '0.2937'}
{'epoch': 99, 'rec_avg_loss': '0.4463', 'joint_avg_loss': '0.7051', 'cl_avg_loss': '2.5882', 'cl_pair_0_loss': '2.5882'}
{'Epoch': 99, 'HIT@5': '0.3486', 'NDCG@5': '0.2402', 'HIT@10': '0.4352', 'NDCG@10': '0.2681', 'HIT@20': '0.5400', 'NDCG@20': '0.2946'}
{'epoch': 100, 'rec_avg_loss': '0.4416', 'joint_avg_loss': '0.7017', 'cl_avg_loss': '2.6008', 'cl_pair_0_loss': '2.6008'}
{'Epoch': 100, 'HIT@5': '0.3493', 'NDCG@5': '0.2406', 'HIT@10': '0.4346', 'NDCG@10': '0.2681', 'HIT@20': '0.5400', 'NDCG@20': '0.2948'}
{'epoch': 101, 'rec_avg_loss': '0.4444', 'joint_avg_loss': '0.7017', 'cl_avg_loss': '2.5738', 'cl_pair_0_loss': '2.5738'}
{'Epoch': 101, 'HIT@5': '0.3487', 'NDCG@5': '0.2406', 'HIT@10': '0.4379', 'NDCG@10': '0.2693', 'HIT@20': '0.5413', 'NDCG@20': '0.2953'}
{'epoch': 102, 'rec_avg_loss': '0.4457', 'joint_avg_loss': '0.7049', 'cl_avg_loss': '2.5917', 'cl_pair_0_loss': '2.5917'}
{'Epoch': 102, 'HIT@5': '0.3496', 'NDCG@5': '0.2416', 'HIT@10': '0.4372', 'NDCG@10': '0.2698', 'HIT@20': '0.5420', 'NDCG@20': '0.2963'}
{'epoch': 103, 'rec_avg_loss': '0.4411', 'joint_avg_loss': '0.6993', 'cl_avg_loss': '2.5821', 'cl_pair_0_loss': '2.5821'}
{'Epoch': 103, 'HIT@5': '0.3489', 'NDCG@5': '0.2397', 'HIT@10': '0.4376', 'NDCG@10': '0.2683', 'HIT@20': '0.5402', 'NDCG@20': '0.2941'}
{'epoch': 104, 'rec_avg_loss': '0.4437', 'joint_avg_loss': '0.7024', 'cl_avg_loss': '2.5864', 'cl_pair_0_loss': '2.5864'}
{'Epoch': 104, 'HIT@5': '0.3454', 'NDCG@5': '0.2364', 'HIT@10': '0.4371', 'NDCG@10': '0.2660', 'HIT@20': '0.5407', 'NDCG@20': '0.2921'}
{'epoch': 105, 'rec_avg_loss': '0.4415', 'joint_avg_loss': '0.7002', 'cl_avg_loss': '2.5874', 'cl_pair_0_loss': '2.5874'}
{'Epoch': 105, 'HIT@5': '0.3491', 'NDCG@5': '0.2402', 'HIT@10': '0.4348', 'NDCG@10': '0.2679', 'HIT@20': '0.5424', 'NDCG@20': '0.2950'}
{'epoch': 106, 'rec_avg_loss': '0.4416', 'joint_avg_loss': '0.6985', 'cl_avg_loss': '2.5699', 'cl_pair_0_loss': '2.5699'}
{'Epoch': 106, 'HIT@5': '0.3480', 'NDCG@5': '0.2390', 'HIT@10': '0.4358', 'NDCG@10': '0.2672', 'HIT@20': '0.5409', 'NDCG@20': '0.2937'}
{'epoch': 107, 'rec_avg_loss': '0.4395', 'joint_avg_loss': '0.6981', 'cl_avg_loss': '2.5858', 'cl_pair_0_loss': '2.5858'}
{'Epoch': 107, 'HIT@5': '0.3483', 'NDCG@5': '0.2394', 'HIT@10': '0.4343', 'NDCG@10': '0.2671', 'HIT@20': '0.5391', 'NDCG@20': '0.2936'}
{'epoch': 108, 'rec_avg_loss': '0.4412', 'joint_avg_loss': '0.6985', 'cl_avg_loss': '2.5735', 'cl_pair_0_loss': '2.5735'}
{'Epoch': 108, 'HIT@5': '0.3474', 'NDCG@5': '0.2361', 'HIT@10': '0.4354', 'NDCG@10': '0.2645', 'HIT@20': '0.5420', 'NDCG@20': '0.2915'}
{'epoch': 109, 'rec_avg_loss': '0.4425', 'joint_avg_loss': '0.6963', 'cl_avg_loss': '2.5386', 'cl_pair_0_loss': '2.5386'}
{'Epoch': 109, 'HIT@5': '0.3459', 'NDCG@5': '0.2345', 'HIT@10': '0.4358', 'NDCG@10': '0.2635', 'HIT@20': '0.5416', 'NDCG@20': '0.2901'}
{'epoch': 110, 'rec_avg_loss': '0.4398', 'joint_avg_loss': '0.6974', 'cl_avg_loss': '2.5767', 'cl_pair_0_loss': '2.5767'}
{'Epoch': 110, 'HIT@5': '0.3479', 'NDCG@5': '0.2389', 'HIT@10': '0.4370', 'NDCG@10': '0.2677', 'HIT@20': '0.5431', 'NDCG@20': '0.2944'}
{'epoch': 111, 'rec_avg_loss': '0.4386', 'joint_avg_loss': '0.6946', 'cl_avg_loss': '2.5604', 'cl_pair_0_loss': '2.5604'}
{'Epoch': 111, 'HIT@5': '0.3481', 'NDCG@5': '0.2379', 'HIT@10': '0.4379', 'NDCG@10': '0.2668', 'HIT@20': '0.5416', 'NDCG@20': '0.2930'}
{'epoch': 112, 'rec_avg_loss': '0.4389', 'joint_avg_loss': '0.6956', 'cl_avg_loss': '2.5667', 'cl_pair_0_loss': '2.5667'}
{'Epoch': 112, 'HIT@5': '0.3462', 'NDCG@5': '0.2383', 'HIT@10': '0.4359', 'NDCG@10': '0.2672', 'HIT@20': '0.5421', 'NDCG@20': '0.2941'}
{'epoch': 113, 'rec_avg_loss': '0.4407', 'joint_avg_loss': '0.6973', 'cl_avg_loss': '2.5656', 'cl_pair_0_loss': '2.5656'}
{'Epoch': 113, 'HIT@5': '0.3464', 'NDCG@5': '0.2378', 'HIT@10': '0.4364', 'NDCG@10': '0.2669', 'HIT@20': '0.5414', 'NDCG@20': '0.2934'}
{'epoch': 114, 'rec_avg_loss': '0.4395', 'joint_avg_loss': '0.6965', 'cl_avg_loss': '2.5698', 'cl_pair_0_loss': '2.5698'}
{'Epoch': 114, 'HIT@5': '0.3484', 'NDCG@5': '0.2392', 'HIT@10': '0.4347', 'NDCG@10': '0.2670', 'HIT@20': '0.5417', 'NDCG@20': '0.2940'}
{'epoch': 115, 'rec_avg_loss': '0.4363', 'joint_avg_loss': '0.6926', 'cl_avg_loss': '2.5632', 'cl_pair_0_loss': '2.5632'}
{'Epoch': 115, 'HIT@5': '0.3480', 'NDCG@5': '0.2362', 'HIT@10': '0.4349', 'NDCG@10': '0.2642', 'HIT@20': '0.5434', 'NDCG@20': '0.2916'}
{'epoch': 116, 'rec_avg_loss': '0.4362', 'joint_avg_loss': '0.6943', 'cl_avg_loss': '2.5811', 'cl_pair_0_loss': '2.5811'}
{'Epoch': 116, 'HIT@5': '0.3457', 'NDCG@5': '0.2341', 'HIT@10': '0.4357', 'NDCG@10': '0.2632', 'HIT@20': '0.5402', 'NDCG@20': '0.2895'}
{'epoch': 117, 'rec_avg_loss': '0.4401', 'joint_avg_loss': '0.6915', 'cl_avg_loss': '2.5145', 'cl_pair_0_loss': '2.5145'}
{'Epoch': 117, 'HIT@5': '0.3457', 'NDCG@5': '0.2336', 'HIT@10': '0.4339', 'NDCG@10': '0.2621', 'HIT@20': '0.5416', 'NDCG@20': '0.2893'}
{'epoch': 118, 'rec_avg_loss': '0.4380', 'joint_avg_loss': '0.6944', 'cl_avg_loss': '2.5646', 'cl_pair_0_loss': '2.5646'}
{'Epoch': 118, 'HIT@5': '0.3473', 'NDCG@5': '0.2342', 'HIT@10': '0.4368', 'NDCG@10': '0.2630', 'HIT@20': '0.5421', 'NDCG@20': '0.2896'}
{'epoch': 119, 'rec_avg_loss': '0.4365', 'joint_avg_loss': '0.6929', 'cl_avg_loss': '2.5639', 'cl_pair_0_loss': '2.5639'}
{'Epoch': 119, 'HIT@5': '0.3470', 'NDCG@5': '0.2354', 'HIT@10': '0.4376', 'NDCG@10': '0.2646', 'HIT@20': '0.5430', 'NDCG@20': '0.2911'}
{'epoch': 120, 'rec_avg_loss': '0.4360', 'joint_avg_loss': '0.6927', 'cl_avg_loss': '2.5678', 'cl_pair_0_loss': '2.5678'}
{'Epoch': 120, 'HIT@5': '0.3434', 'NDCG@5': '0.2313', 'HIT@10': '0.4347', 'NDCG@10': '0.2607', 'HIT@20': '0.5428', 'NDCG@20': '0.2880'}
{'epoch': 121, 'rec_avg_loss': '0.4375', 'joint_avg_loss': '0.6915', 'cl_avg_loss': '2.5399', 'cl_pair_0_loss': '2.5399'}
{'Epoch': 121, 'HIT@5': '0.3466', 'NDCG@5': '0.2336', 'HIT@10': '0.4373', 'NDCG@10': '0.2628', 'HIT@20': '0.5435', 'NDCG@20': '0.2896'}
{'epoch': 122, 'rec_avg_loss': '0.4387', 'joint_avg_loss': '0.6936', 'cl_avg_loss': '2.5483', 'cl_pair_0_loss': '2.5483'}
{'Epoch': 122, 'HIT@5': '0.3465', 'NDCG@5': '0.2338', 'HIT@10': '0.4368', 'NDCG@10': '0.2630', 'HIT@20': '0.5427', 'NDCG@20': '0.2896'}
{'epoch': 123, 'rec_avg_loss': '0.4375', 'joint_avg_loss': '0.6914', 'cl_avg_loss': '2.5395', 'cl_pair_0_loss': '2.5395'}
{'Epoch': 123, 'HIT@5': '0.3474', 'NDCG@5': '0.2375', 'HIT@10': '0.4369', 'NDCG@10': '0.2663', 'HIT@20': '0.5448', 'NDCG@20': '0.2936'}
{'epoch': 124, 'rec_avg_loss': '0.4372', 'joint_avg_loss': '0.6907', 'cl_avg_loss': '2.5350', 'cl_pair_0_loss': '2.5350'}
{'Epoch': 124, 'HIT@5': '0.3466', 'NDCG@5': '0.2336', 'HIT@10': '0.4373', 'NDCG@10': '0.2628', 'HIT@20': '0.5443', 'NDCG@20': '0.2898'}
{'epoch': 125, 'rec_avg_loss': '0.4350', 'joint_avg_loss': '0.6873', 'cl_avg_loss': '2.5235', 'cl_pair_0_loss': '2.5235'}
{'Epoch': 125, 'HIT@5': '0.3473', 'NDCG@5': '0.2367', 'HIT@10': '0.4354', 'NDCG@10': '0.2650', 'HIT@20': '0.5438', 'NDCG@20': '0.2924'}
{'epoch': 126, 'rec_avg_loss': '0.4338', 'joint_avg_loss': '0.6866', 'cl_avg_loss': '2.5280', 'cl_pair_0_loss': '2.5280'}
{'Epoch': 126, 'HIT@5': '0.3477', 'NDCG@5': '0.2349', 'HIT@10': '0.4381', 'NDCG@10': '0.2640', 'HIT@20': '0.5447', 'NDCG@20': '0.2909'}
{'epoch': 127, 'rec_avg_loss': '0.4363', 'joint_avg_loss': '0.6900', 'cl_avg_loss': '2.5370', 'cl_pair_0_loss': '2.5370'}
{'Epoch': 127, 'HIT@5': '0.3461', 'NDCG@5': '0.2335', 'HIT@10': '0.4401', 'NDCG@10': '0.2638', 'HIT@20': '0.5454', 'NDCG@20': '0.2903'}
{'epoch': 128, 'rec_avg_loss': '0.4359', 'joint_avg_loss': '0.6896', 'cl_avg_loss': '2.5368', 'cl_pair_0_loss': '2.5368'}
{'Epoch': 128, 'HIT@5': '0.3472', 'NDCG@5': '0.2336', 'HIT@10': '0.4403', 'NDCG@10': '0.2635', 'HIT@20': '0.5446', 'NDCG@20': '0.2899'}
{'epoch': 129, 'rec_avg_loss': '0.4351', 'joint_avg_loss': '0.6865', 'cl_avg_loss': '2.5145', 'cl_pair_0_loss': '2.5145'}
{'Epoch': 129, 'HIT@5': '0.3464', 'NDCG@5': '0.2344', 'HIT@10': '0.4402', 'NDCG@10': '0.2646', 'HIT@20': '0.5449', 'NDCG@20': '0.2910'}
{'epoch': 130, 'rec_avg_loss': '0.4337', 'joint_avg_loss': '0.6874', 'cl_avg_loss': '2.5377', 'cl_pair_0_loss': '2.5377'}
{'Epoch': 130, 'HIT@5': '0.3462', 'NDCG@5': '0.2307', 'HIT@10': '0.4373', 'NDCG@10': '0.2601', 'HIT@20': '0.5442', 'NDCG@20': '0.2870'}
{'epoch': 131, 'rec_avg_loss': '0.4324', 'joint_avg_loss': '0.6863', 'cl_avg_loss': '2.5393', 'cl_pair_0_loss': '2.5393'}
{'Epoch': 131, 'HIT@5': '0.3485', 'NDCG@5': '0.2349', 'HIT@10': '0.4377', 'NDCG@10': '0.2637', 'HIT@20': '0.5440', 'NDCG@20': '0.2906'}
{'epoch': 132, 'rec_avg_loss': '0.4314', 'joint_avg_loss': '0.6839', 'cl_avg_loss': '2.5249', 'cl_pair_0_loss': '2.5249'}
{'Epoch': 132, 'HIT@5': '0.3475', 'NDCG@5': '0.2338', 'HIT@10': '0.4375', 'NDCG@10': '0.2629', 'HIT@20': '0.5429', 'NDCG@20': '0.2895'}
{'epoch': 133, 'rec_avg_loss': '0.4318', 'joint_avg_loss': '0.6827', 'cl_avg_loss': '2.5092', 'cl_pair_0_loss': '2.5092'}
{'Epoch': 133, 'HIT@5': '0.3474', 'NDCG@5': '0.2353', 'HIT@10': '0.4385', 'NDCG@10': '0.2646', 'HIT@20': '0.5425', 'NDCG@20': '0.2909'}
{'epoch': 134, 'rec_avg_loss': '0.4326', 'joint_avg_loss': '0.6841', 'cl_avg_loss': '2.5151', 'cl_pair_0_loss': '2.5151'}
{'Epoch': 134, 'HIT@5': '0.3480', 'NDCG@5': '0.2348', 'HIT@10': '0.4373', 'NDCG@10': '0.2636', 'HIT@20': '0.5428', 'NDCG@20': '0.2903'}
{'epoch': 135, 'rec_avg_loss': '0.4318', 'joint_avg_loss': '0.6825', 'cl_avg_loss': '2.5065', 'cl_pair_0_loss': '2.5065'}
{'Epoch': 135, 'HIT@5': '0.3476', 'NDCG@5': '0.2339', 'HIT@10': '0.4381', 'NDCG@10': '0.2631', 'HIT@20': '0.5442', 'NDCG@20': '0.2899'}
{'epoch': 136, 'rec_avg_loss': '0.4328', 'joint_avg_loss': '0.6834', 'cl_avg_loss': '2.5055', 'cl_pair_0_loss': '2.5055'}
{'Epoch': 136, 'HIT@5': '0.3467', 'NDCG@5': '0.2336', 'HIT@10': '0.4387', 'NDCG@10': '0.2632', 'HIT@20': '0.5438', 'NDCG@20': '0.2897'}
{'epoch': 137, 'rec_avg_loss': '0.4350', 'joint_avg_loss': '0.6862', 'cl_avg_loss': '2.5114', 'cl_pair_0_loss': '2.5114'}
{'Epoch': 137, 'HIT@5': '0.3476', 'NDCG@5': '0.2345', 'HIT@10': '0.4376', 'NDCG@10': '0.2635', 'HIT@20': '0.5437', 'NDCG@20': '0.2903'}
{'Epoch': 0, 'HIT@5': '0.3284', 'NDCG@5': '0.2760', 'HIT@10': '0.4138', 'NDCG@10': '0.3036', 'HIT@20': '0.5084', 'NDCG@20': '0.3275'}
CoSeRec-mooc-0
{'Epoch': 0, 'HIT@5': '0.3284', 'NDCG@5': '0.2760', 'HIT@10': '0.4138', 'NDCG@10': '0.3036', 'HIT@20': '0.5084', 'NDCG@20': '0.3275'}
Namespace(data_dir='../data/', output_dir='output/', data_name='mooc', do_eval=False, model_idx=0, gpu_id='0', noise_ratio=0.0, training_data_ratio=1.0, augment_threshold=4, similarity_model_name='ItemCF_IUF', augmentation_warm_up_epoches=300, base_augment_type='random', augment_type_for_short='SIM', tao=0.2, gamma=0.7, beta=0.2, substitute_rate=0.1, insert_rate=0.4, max_insert_num_per_pos=1, temperature=1.0, n_views=2, model_name='CoSeRec', hidden_size=64, num_hidden_layers=2, num_attention_heads=2, hidden_act='gelu', attention_probs_dropout_prob=0.5, hidden_dropout_prob=0.5, initializer_range=0.02, max_seq_length=50, lr=0.001, batch_size=256, epochs=3, no_cuda=False, log_freq=1, seed=1, cf_weight=0.1, rec_weight=1.0, weight_decay=0.0, adam_beta1=0.9, adam_beta2=0.999, device_type='mps', cuda_condition=False, data_file='../data/mooc.txt', item_size=1304, mask_id=1303, log_file='output/CoSeRec-mooc-0.txt')
{'epoch': 0, 'rec_avg_loss': '1.0603', 'joint_avg_loss': '4.5332', 'cl_avg_loss': '34.7282', 'cl_pair_0_loss': '34.7282'}
{'Epoch': 0, 'HIT@5': '0.0661', 'NDCG@5': '0.0403', 'HIT@10': '0.1124', 'NDCG@10': '0.0551', 'HIT@20': '0.1995', 'NDCG@20': '0.0769'}
{'epoch': 1, 'rec_avg_loss': '0.8501', 'joint_avg_loss': '2.2230', 'cl_avg_loss': '13.7293', 'cl_pair_0_loss': '13.7293'}
{'Epoch': 1, 'HIT@5': '0.0874', 'NDCG@5': '0.0528', 'HIT@10': '0.1454', 'NDCG@10': '0.0716', 'HIT@20': '0.2169', 'NDCG@20': '0.0896'}
{'epoch': 2, 'rec_avg_loss': '0.7881', 'joint_avg_loss': '1.6196', 'cl_avg_loss': '8.3148', 'cl_pair_0_loss': '8.3148'}
{'Epoch': 2, 'HIT@5': '0.0941', 'NDCG@5': '0.0606', 'HIT@10': '0.1554', 'NDCG@10': '0.0804', 'HIT@20': '0.2275', 'NDCG@20': '0.0985'}
{'Epoch': 0, 'HIT@5': '0.0904', 'NDCG@5': '0.0502', 'HIT@10': '0.2361', 'NDCG@10': '0.0975', 'HIT@20': '0.3094', 'NDCG@20': '0.1160'}
CoSeRec-mooc-0
{'Epoch': 0, 'HIT@5': '0.0904', 'NDCG@5': '0.0502', 'HIT@10': '0.2361', 'NDCG@10': '0.0975', 'HIT@20': '0.3094', 'NDCG@20': '0.1160'}
Namespace(data_dir='../data/', output_dir='output/', data_name='mooc', do_eval=False, model_idx=0, gpu_id='0', noise_ratio=0.0, training_data_ratio=1.0, augment_threshold=4, similarity_model_name='ItemCF_IUF', augmentation_warm_up_epoches=300, base_augment_type='random', augment_type_for_short='SIM', tao=0.2, gamma=0.7, beta=0.2, substitute_rate=0.1, insert_rate=0.4, max_insert_num_per_pos=1, temperature=1.0, n_views=2, model_name='CoSeRec', hidden_size=64, num_hidden_layers=2, num_attention_heads=2, hidden_act='gelu', attention_probs_dropout_prob=0.5, hidden_dropout_prob=0.5, initializer_range=0.02, max_seq_length=50, lr=0.001, batch_size=256, epochs=3, no_cuda=False, log_freq=1, seed=1, cf_weight=0.1, rec_weight=1.0, weight_decay=0.0, adam_beta1=0.9, adam_beta2=0.999, device_type='mps', cuda_condition=False, data_file='../data/mooc.txt', item_size=1304, mask_id=1303, log_file='output/CoSeRec-mooc-0.txt')
{'epoch': 0, 'rec_avg_loss': '1.0603', 'joint_avg_loss': '4.5332', 'cl_avg_loss': '34.7282', 'cl_pair_0_loss': '34.7282'}
{'Epoch': 0, 'HIT@5': '0.0661', 'NDCG@5': '0.0403', 'HIT@10': '0.1124', 'NDCG@10': '0.0551', 'HIT@20': '0.1995', 'NDCG@20': '0.0769'}
{'epoch': 1, 'rec_avg_loss': '0.8501', 'joint_avg_loss': '2.2230', 'cl_avg_loss': '13.7293', 'cl_pair_0_loss': '13.7293'}
{'Epoch': 1, 'HIT@5': '0.0874', 'NDCG@5': '0.0528', 'HIT@10': '0.1454', 'NDCG@10': '0.0716', 'HIT@20': '0.2169', 'NDCG@20': '0.0896'}
{'epoch': 2, 'rec_avg_loss': '0.7881', 'joint_avg_loss': '1.6196', 'cl_avg_loss': '8.3148', 'cl_pair_0_loss': '8.3148'}
{'Epoch': 2, 'HIT@5': '0.0941', 'NDCG@5': '0.0606', 'HIT@10': '0.1554', 'NDCG@10': '0.0804', 'HIT@20': '0.2275', 'NDCG@20': '0.0985'}
{'Epoch': 0, 'HIT@5': '0.0904', 'NDCG@5': '0.0502', 'HIT@10': '0.2361', 'NDCG@10': '0.0975', 'HIT@20': '0.3094', 'NDCG@20': '0.1160'}
CoSeRec-mooc-0
{'Epoch': 0, 'HIT@5': '0.0904', 'NDCG@5': '0.0502', 'HIT@10': '0.2361', 'NDCG@10': '0.0975', 'HIT@20': '0.3094', 'NDCG@20': '0.1160'}
Namespace(data_dir='../data/', output_dir='output/', data_name='mooc', do_eval=False, model_idx=0, gpu_id='0', noise_ratio=0.0, training_data_ratio=1.0, augment_threshold=4, similarity_model_name='ItemCF_IUF', augmentation_warm_up_epoches=300, base_augment_type='random', augment_type_for_short='SIM', tao=0.2, gamma=0.7, beta=0.2, substitute_rate=0.1, insert_rate=0.4, max_insert_num_per_pos=1, temperature=1.0, n_views=2, model_name='CoSeRec', hidden_size=64, num_hidden_layers=2, num_attention_heads=2, hidden_act='gelu', attention_probs_dropout_prob=0.5, hidden_dropout_prob=0.5, initializer_range=0.02, max_seq_length=50, lr=0.001, batch_size=256, epochs=100, no_cuda=False, log_freq=1, seed=1, cf_weight=0.1, rec_weight=1.0, weight_decay=0.0, adam_beta1=0.9, adam_beta2=0.999, device_type='mps', cuda_condition=False, data_file='../data/mooc.txt', item_size=1304, mask_id=1303, log_file='output/CoSeRec-mooc-0.txt')
{'epoch': 0, 'rec_avg_loss': '1.0603', 'joint_avg_loss': '4.5332', 'cl_avg_loss': '34.7282', 'cl_pair_0_loss': '34.7282'}
{'Epoch': 0, 'HIT@5': '0.0661', 'NDCG@5': '0.0403', 'HIT@10': '0.1124', 'NDCG@10': '0.0551', 'HIT@20': '0.1995', 'NDCG@20': '0.0769'}
{'epoch': 1, 'rec_avg_loss': '0.8501', 'joint_avg_loss': '2.2230', 'cl_avg_loss': '13.7293', 'cl_pair_0_loss': '13.7293'}
{'Epoch': 1, 'HIT@5': '0.0874', 'NDCG@5': '0.0528', 'HIT@10': '0.1454', 'NDCG@10': '0.0716', 'HIT@20': '0.2169', 'NDCG@20': '0.0896'}
{'epoch': 2, 'rec_avg_loss': '0.7881', 'joint_avg_loss': '1.6196', 'cl_avg_loss': '8.3148', 'cl_pair_0_loss': '8.3148'}
{'Epoch': 2, 'HIT@5': '0.0941', 'NDCG@5': '0.0606', 'HIT@10': '0.1554', 'NDCG@10': '0.0804', 'HIT@20': '0.2275', 'NDCG@20': '0.0985'}
{'epoch': 3, 'rec_avg_loss': '0.7528', 'joint_avg_loss': '1.3901', 'cl_avg_loss': '6.3731', 'cl_pair_0_loss': '6.3731'}
{'Epoch': 3, 'HIT@5': '0.1159', 'NDCG@5': '0.0748', 'HIT@10': '0.1664', 'NDCG@10': '0.0913', 'HIT@20': '0.2323', 'NDCG@20': '0.1079'}
{'epoch': 4, 'rec_avg_loss': '0.7291', 'joint_avg_loss': '1.2680', 'cl_avg_loss': '5.3889', 'cl_pair_0_loss': '5.3889'}
{'Epoch': 4, 'HIT@5': '0.1225', 'NDCG@5': '0.0821', 'HIT@10': '0.1753', 'NDCG@10': '0.0994', 'HIT@20': '0.3458', 'NDCG@20': '0.1404'}
{'epoch': 5, 'rec_avg_loss': '0.7136', 'joint_avg_loss': '1.1889', 'cl_avg_loss': '4.7530', 'cl_pair_0_loss': '4.7530'}
{'Epoch': 5, 'HIT@5': '0.1346', 'NDCG@5': '0.0908', 'HIT@10': '0.1859', 'NDCG@10': '0.1073', 'HIT@20': '0.2555', 'NDCG@20': '0.1249'}
{'epoch': 6, 'rec_avg_loss': '0.7008', 'joint_avg_loss': '1.1346', 'cl_avg_loss': '4.3378', 'cl_pair_0_loss': '4.3378'}
{'Epoch': 6, 'HIT@5': '0.1398', 'NDCG@5': '0.0972', 'HIT@10': '0.1943', 'NDCG@10': '0.1147', 'HIT@20': '0.3561', 'NDCG@20': '0.1540'}
{'epoch': 7, 'rec_avg_loss': '0.6894', 'joint_avg_loss': '1.0964', 'cl_avg_loss': '4.0706', 'cl_pair_0_loss': '4.0706'}
{'Epoch': 7, 'HIT@5': '0.1419', 'NDCG@5': '0.0989', 'HIT@10': '0.1944', 'NDCG@10': '0.1159', 'HIT@20': '0.3582', 'NDCG@20': '0.1564'}
{'epoch': 8, 'rec_avg_loss': '0.6845', 'joint_avg_loss': '1.0641', 'cl_avg_loss': '3.7956', 'cl_pair_0_loss': '3.7956'}
{'Epoch': 8, 'HIT@5': '0.1399', 'NDCG@5': '0.0992', 'HIT@10': '0.1946', 'NDCG@10': '0.1168', 'HIT@20': '0.3607', 'NDCG@20': '0.1596'}
{'epoch': 9, 'rec_avg_loss': '0.6794', 'joint_avg_loss': '1.0450', 'cl_avg_loss': '3.6566', 'cl_pair_0_loss': '3.6566'}
{'Epoch': 9, 'HIT@5': '0.1417', 'NDCG@5': '0.1011', 'HIT@10': '0.1973', 'NDCG@10': '0.1191', 'HIT@20': '0.3640', 'NDCG@20': '0.1624'}
{'epoch': 10, 'rec_avg_loss': '0.6764', 'joint_avg_loss': '1.0297', 'cl_avg_loss': '3.5331', 'cl_pair_0_loss': '3.5331'}
{'Epoch': 10, 'HIT@5': '0.1475', 'NDCG@5': '0.1046', 'HIT@10': '0.2549', 'NDCG@10': '0.1379', 'HIT@20': '0.3663', 'NDCG@20': '0.1660'}
{'epoch': 11, 'rec_avg_loss': '0.6691', 'joint_avg_loss': '1.0150', 'cl_avg_loss': '3.4598', 'cl_pair_0_loss': '3.4598'}
{'Epoch': 11, 'HIT@5': '0.1475', 'NDCG@5': '0.1047', 'HIT@10': '0.2608', 'NDCG@10': '0.1412', 'HIT@20': '0.3713', 'NDCG@20': '0.1693'}
{'epoch': 12, 'rec_avg_loss': '0.6673', 'joint_avg_loss': '1.0036', 'cl_avg_loss': '3.3627', 'cl_pair_0_loss': '3.3627'}
{'Epoch': 12, 'HIT@5': '0.1484', 'NDCG@5': '0.1074', 'HIT@10': '0.2653', 'NDCG@10': '0.1436', 'HIT@20': '0.3781', 'NDCG@20': '0.1720'}
{'epoch': 13, 'rec_avg_loss': '0.6621', 'joint_avg_loss': '0.9891', 'cl_avg_loss': '3.2705', 'cl_pair_0_loss': '3.2705'}
{'Epoch': 13, 'HIT@5': '0.1503', 'NDCG@5': '0.1087', 'HIT@10': '0.2625', 'NDCG@10': '0.1441', 'HIT@20': '0.3815', 'NDCG@20': '0.1744'}
{'epoch': 14, 'rec_avg_loss': '0.6552', 'joint_avg_loss': '0.9783', 'cl_avg_loss': '3.2306', 'cl_pair_0_loss': '3.2306'}
{'Epoch': 14, 'HIT@5': '0.1512', 'NDCG@5': '0.1110', 'HIT@10': '0.2656', 'NDCG@10': '0.1473', 'HIT@20': '0.3902', 'NDCG@20': '0.1790'}
{'epoch': 15, 'rec_avg_loss': '0.6514', 'joint_avg_loss': '0.9677', 'cl_avg_loss': '3.1632', 'cl_pair_0_loss': '3.1632'}
{'Epoch': 15, 'HIT@5': '0.1564', 'NDCG@5': '0.1109', 'HIT@10': '0.2709', 'NDCG@10': '0.1473', 'HIT@20': '0.3966', 'NDCG@20': '0.1799'}
{'epoch': 16, 'rec_avg_loss': '0.6482', 'joint_avg_loss': '0.9618', 'cl_avg_loss': '3.1358', 'cl_pair_0_loss': '3.1358'}
{'Epoch': 16, 'HIT@5': '0.1563', 'NDCG@5': '0.1119', 'HIT@10': '0.2663', 'NDCG@10': '0.1467', 'HIT@20': '0.3937', 'NDCG@20': '0.1792'}
{'epoch': 17, 'rec_avg_loss': '0.6458', 'joint_avg_loss': '0.9572', 'cl_avg_loss': '3.1138', 'cl_pair_0_loss': '3.1138'}
{'Epoch': 17, 'HIT@5': '0.1618', 'NDCG@5': '0.1145', 'HIT@10': '0.2711', 'NDCG@10': '0.1493', 'HIT@20': '0.4083', 'NDCG@20': '0.1843'}
{'epoch': 18, 'rec_avg_loss': '0.6389', 'joint_avg_loss': '0.9473', 'cl_avg_loss': '3.0837', 'cl_pair_0_loss': '3.0837'}
{'Epoch': 18, 'HIT@5': '0.1602', 'NDCG@5': '0.1158', 'HIT@10': '0.2768', 'NDCG@10': '0.1525', 'HIT@20': '0.4142', 'NDCG@20': '0.1881'}
{'epoch': 19, 'rec_avg_loss': '0.6357', 'joint_avg_loss': '0.9400', 'cl_avg_loss': '3.0435', 'cl_pair_0_loss': '3.0435'}
{'Epoch': 19, 'HIT@5': '0.1651', 'NDCG@5': '0.1203', 'HIT@10': '0.2812', 'NDCG@10': '0.1571', 'HIT@20': '0.4171', 'NDCG@20': '0.1918'}
{'epoch': 20, 'rec_avg_loss': '0.6295', 'joint_avg_loss': '0.9298', 'cl_avg_loss': '3.0023', 'cl_pair_0_loss': '3.0023'}
{'Epoch': 20, 'HIT@5': '0.2193', 'NDCG@5': '0.1417', 'HIT@10': '0.3273', 'NDCG@10': '0.1755', 'HIT@20': '0.4268', 'NDCG@20': '0.2004'}
{'epoch': 21, 'rec_avg_loss': '0.6251', 'joint_avg_loss': '0.9262', 'cl_avg_loss': '3.0103', 'cl_pair_0_loss': '3.0103'}
{'Epoch': 21, 'HIT@5': '0.2243', 'NDCG@5': '0.1462', 'HIT@10': '0.3315', 'NDCG@10': '0.1810', 'HIT@20': '0.4296', 'NDCG@20': '0.2055'}
{'epoch': 22, 'rec_avg_loss': '0.6180', 'joint_avg_loss': '0.9166', 'cl_avg_loss': '2.9859', 'cl_pair_0_loss': '2.9859'}
{'Epoch': 22, 'HIT@5': '0.2300', 'NDCG@5': '0.1523', 'HIT@10': '0.3372', 'NDCG@10': '0.1873', 'HIT@20': '0.4403', 'NDCG@20': '0.2133'}
{'epoch': 23, 'rec_avg_loss': '0.6119', 'joint_avg_loss': '0.9126', 'cl_avg_loss': '3.0072', 'cl_pair_0_loss': '3.0072'}
{'Epoch': 23, 'HIT@5': '0.2713', 'NDCG@5': '0.1781', 'HIT@10': '0.3417', 'NDCG@10': '0.2010', 'HIT@20': '0.4444', 'NDCG@20': '0.2269'}
{'epoch': 24, 'rec_avg_loss': '0.6074', 'joint_avg_loss': '0.9033', 'cl_avg_loss': '2.9597', 'cl_pair_0_loss': '2.9597'}
{'Epoch': 24, 'HIT@5': '0.2377', 'NDCG@5': '0.1627', 'HIT@10': '0.3482', 'NDCG@10': '0.1996', 'HIT@20': '0.4492', 'NDCG@20': '0.2251'}
{'epoch': 25, 'rec_avg_loss': '0.6023', 'joint_avg_loss': '0.8993', 'cl_avg_loss': '2.9701', 'cl_pair_0_loss': '2.9701'}
{'Epoch': 25, 'HIT@5': '0.2721', 'NDCG@5': '0.1737', 'HIT@10': '0.3459', 'NDCG@10': '0.1976', 'HIT@20': '0.4539', 'NDCG@20': '0.2248'}
{'epoch': 26, 'rec_avg_loss': '0.5938', 'joint_avg_loss': '0.8890', 'cl_avg_loss': '2.9519', 'cl_pair_0_loss': '2.9519'}
{'Epoch': 26, 'HIT@5': '0.2864', 'NDCG@5': '0.1876', 'HIT@10': '0.3577', 'NDCG@10': '0.2106', 'HIT@20': '0.4621', 'NDCG@20': '0.2371'}
{'epoch': 27, 'rec_avg_loss': '0.5913', 'joint_avg_loss': '0.8858', 'cl_avg_loss': '2.9446', 'cl_pair_0_loss': '2.9446'}
{'Epoch': 27, 'HIT@5': '0.2902', 'NDCG@5': '0.1899', 'HIT@10': '0.3645', 'NDCG@10': '0.2138', 'HIT@20': '0.4636', 'NDCG@20': '0.2388'}
{'epoch': 28, 'rec_avg_loss': '0.5847', 'joint_avg_loss': '0.8798', 'cl_avg_loss': '2.9512', 'cl_pair_0_loss': '2.9512'}
{'Epoch': 28, 'HIT@5': '0.2929', 'NDCG@5': '0.1967', 'HIT@10': '0.3708', 'NDCG@10': '0.2218', 'HIT@20': '0.4732', 'NDCG@20': '0.2477'}
{'epoch': 29, 'rec_avg_loss': '0.5825', 'joint_avg_loss': '0.8768', 'cl_avg_loss': '2.9430', 'cl_pair_0_loss': '2.9430'}
{'Epoch': 29, 'HIT@5': '0.3015', 'NDCG@5': '0.1996', 'HIT@10': '0.3787', 'NDCG@10': '0.2244', 'HIT@20': '0.4759', 'NDCG@20': '0.2490'}
{'epoch': 30, 'rec_avg_loss': '0.5761', 'joint_avg_loss': '0.8710', 'cl_avg_loss': '2.9493', 'cl_pair_0_loss': '2.9493'}
{'Epoch': 30, 'HIT@5': '0.3037', 'NDCG@5': '0.2097', 'HIT@10': '0.3799', 'NDCG@10': '0.2341', 'HIT@20': '0.4776', 'NDCG@20': '0.2588'}
{'epoch': 31, 'rec_avg_loss': '0.5722', 'joint_avg_loss': '0.8636', 'cl_avg_loss': '2.9142', 'cl_pair_0_loss': '2.9142'}
{'Epoch': 31, 'HIT@5': '0.3099', 'NDCG@5': '0.2154', 'HIT@10': '0.3871', 'NDCG@10': '0.2402', 'HIT@20': '0.4820', 'NDCG@20': '0.2641'}
{'epoch': 32, 'rec_avg_loss': '0.5657', 'joint_avg_loss': '0.8575', 'cl_avg_loss': '2.9186', 'cl_pair_0_loss': '2.9186'}
{'Epoch': 32, 'HIT@5': '0.3107', 'NDCG@5': '0.2164', 'HIT@10': '0.3918', 'NDCG@10': '0.2424', 'HIT@20': '0.4834', 'NDCG@20': '0.2655'}
{'epoch': 33, 'rec_avg_loss': '0.5655', 'joint_avg_loss': '0.8574', 'cl_avg_loss': '2.9185', 'cl_pair_0_loss': '2.9185'}
{'Epoch': 33, 'HIT@5': '0.3153', 'NDCG@5': '0.2194', 'HIT@10': '0.3975', 'NDCG@10': '0.2458', 'HIT@20': '0.4882', 'NDCG@20': '0.2687'}
{'epoch': 34, 'rec_avg_loss': '0.5587', 'joint_avg_loss': '0.8477', 'cl_avg_loss': '2.8897', 'cl_pair_0_loss': '2.8897'}
{'Epoch': 34, 'HIT@5': '0.3162', 'NDCG@5': '0.2189', 'HIT@10': '0.3967', 'NDCG@10': '0.2448', 'HIT@20': '0.4911', 'NDCG@20': '0.2688'}
{'epoch': 35, 'rec_avg_loss': '0.5534', 'joint_avg_loss': '0.8457', 'cl_avg_loss': '2.9226', 'cl_pair_0_loss': '2.9226'}
{'Epoch': 35, 'HIT@5': '0.3227', 'NDCG@5': '0.2258', 'HIT@10': '0.4011', 'NDCG@10': '0.2512', 'HIT@20': '0.4955', 'NDCG@20': '0.2750'}
{'epoch': 36, 'rec_avg_loss': '0.5488', 'joint_avg_loss': '0.8373', 'cl_avg_loss': '2.8844', 'cl_pair_0_loss': '2.8844'}
{'Epoch': 36, 'HIT@5': '0.3296', 'NDCG@5': '0.2283', 'HIT@10': '0.4105', 'NDCG@10': '0.2546', 'HIT@20': '0.5048', 'NDCG@20': '0.2784'}
{'epoch': 37, 'rec_avg_loss': '0.5429', 'joint_avg_loss': '0.8336', 'cl_avg_loss': '2.9066', 'cl_pair_0_loss': '2.9066'}
{'Epoch': 37, 'HIT@5': '0.3306', 'NDCG@5': '0.2292', 'HIT@10': '0.4118', 'NDCG@10': '0.2556', 'HIT@20': '0.5022', 'NDCG@20': '0.2783'}
{'epoch': 38, 'rec_avg_loss': '0.5411', 'joint_avg_loss': '0.8309', 'cl_avg_loss': '2.8976', 'cl_pair_0_loss': '2.8976'}
{'Epoch': 38, 'HIT@5': '0.3336', 'NDCG@5': '0.2334', 'HIT@10': '0.4119', 'NDCG@10': '0.2587', 'HIT@20': '0.5053', 'NDCG@20': '0.2823'}
{'epoch': 39, 'rec_avg_loss': '0.5373', 'joint_avg_loss': '0.8274', 'cl_avg_loss': '2.9010', 'cl_pair_0_loss': '2.9010'}
{'Epoch': 39, 'HIT@5': '0.3296', 'NDCG@5': '0.2285', 'HIT@10': '0.4122', 'NDCG@10': '0.2553', 'HIT@20': '0.5061', 'NDCG@20': '0.2790'}
{'epoch': 40, 'rec_avg_loss': '0.5331', 'joint_avg_loss': '0.8203', 'cl_avg_loss': '2.8724', 'cl_pair_0_loss': '2.8724'}
{'Epoch': 40, 'HIT@5': '0.3378', 'NDCG@5': '0.2349', 'HIT@10': '0.4157', 'NDCG@10': '0.2600', 'HIT@20': '0.5103', 'NDCG@20': '0.2838'}
{'epoch': 41, 'rec_avg_loss': '0.5277', 'joint_avg_loss': '0.8171', 'cl_avg_loss': '2.8945', 'cl_pair_0_loss': '2.8945'}
{'Epoch': 41, 'HIT@5': '0.3403', 'NDCG@5': '0.2369', 'HIT@10': '0.4153', 'NDCG@10': '0.2610', 'HIT@20': '0.5105', 'NDCG@20': '0.2850'}
{'epoch': 42, 'rec_avg_loss': '0.5250', 'joint_avg_loss': '0.8142', 'cl_avg_loss': '2.8926', 'cl_pair_0_loss': '2.8926'}
{'Epoch': 42, 'HIT@5': '0.3447', 'NDCG@5': '0.2397', 'HIT@10': '0.4209', 'NDCG@10': '0.2643', 'HIT@20': '0.5150', 'NDCG@20': '0.2880'}
{'epoch': 43, 'rec_avg_loss': '0.5220', 'joint_avg_loss': '0.8103', 'cl_avg_loss': '2.8833', 'cl_pair_0_loss': '2.8833'}
{'Epoch': 43, 'HIT@5': '0.3468', 'NDCG@5': '0.2418', 'HIT@10': '0.4227', 'NDCG@10': '0.2663', 'HIT@20': '0.5172', 'NDCG@20': '0.2902'}
{'epoch': 44, 'rec_avg_loss': '0.5197', 'joint_avg_loss': '0.8041', 'cl_avg_loss': '2.8449', 'cl_pair_0_loss': '2.8449'}
{'Epoch': 44, 'HIT@5': '0.3460', 'NDCG@5': '0.2409', 'HIT@10': '0.4209', 'NDCG@10': '0.2650', 'HIT@20': '0.5153', 'NDCG@20': '0.2888'}
{'epoch': 45, 'rec_avg_loss': '0.5119', 'joint_avg_loss': '0.7985', 'cl_avg_loss': '2.8658', 'cl_pair_0_loss': '2.8658'}
{'Epoch': 45, 'HIT@5': '0.3437', 'NDCG@5': '0.2391', 'HIT@10': '0.4201', 'NDCG@10': '0.2637', 'HIT@20': '0.5174', 'NDCG@20': '0.2883'}
{'epoch': 46, 'rec_avg_loss': '0.5103', 'joint_avg_loss': '0.7951', 'cl_avg_loss': '2.8487', 'cl_pair_0_loss': '2.8487'}
{'Epoch': 46, 'HIT@5': '0.3433', 'NDCG@5': '0.2380', 'HIT@10': '0.4210', 'NDCG@10': '0.2632', 'HIT@20': '0.5182', 'NDCG@20': '0.2877'}
{'epoch': 47, 'rec_avg_loss': '0.5068', 'joint_avg_loss': '0.7905', 'cl_avg_loss': '2.8371', 'cl_pair_0_loss': '2.8371'}
{'Epoch': 47, 'HIT@5': '0.3406', 'NDCG@5': '0.2367', 'HIT@10': '0.4229', 'NDCG@10': '0.2632', 'HIT@20': '0.5219', 'NDCG@20': '0.2883'}
{'epoch': 48, 'rec_avg_loss': '0.5055', 'joint_avg_loss': '0.7920', 'cl_avg_loss': '2.8645', 'cl_pair_0_loss': '2.8645'}
{'Epoch': 48, 'HIT@5': '0.3456', 'NDCG@5': '0.2410', 'HIT@10': '0.4270', 'NDCG@10': '0.2673', 'HIT@20': '0.5240', 'NDCG@20': '0.2916'}
{'epoch': 49, 'rec_avg_loss': '0.5002', 'joint_avg_loss': '0.7839', 'cl_avg_loss': '2.8368', 'cl_pair_0_loss': '2.8368'}
{'Epoch': 49, 'HIT@5': '0.3471', 'NDCG@5': '0.2443', 'HIT@10': '0.4256', 'NDCG@10': '0.2695', 'HIT@20': '0.5245', 'NDCG@20': '0.2945'}
{'epoch': 50, 'rec_avg_loss': '0.4985', 'joint_avg_loss': '0.7804', 'cl_avg_loss': '2.8196', 'cl_pair_0_loss': '2.8196'}
{'Epoch': 50, 'HIT@5': '0.3487', 'NDCG@5': '0.2436', 'HIT@10': '0.4259', 'NDCG@10': '0.2685', 'HIT@20': '0.5246', 'NDCG@20': '0.2933'}
{'epoch': 51, 'rec_avg_loss': '0.4958', 'joint_avg_loss': '0.7768', 'cl_avg_loss': '2.8099', 'cl_pair_0_loss': '2.8099'}
{'Epoch': 51, 'HIT@5': '0.3481', 'NDCG@5': '0.2438', 'HIT@10': '0.4285', 'NDCG@10': '0.2698', 'HIT@20': '0.5244', 'NDCG@20': '0.2940'}
{'epoch': 52, 'rec_avg_loss': '0.4942', 'joint_avg_loss': '0.7776', 'cl_avg_loss': '2.8343', 'cl_pair_0_loss': '2.8343'}
{'Epoch': 52, 'HIT@5': '0.3482', 'NDCG@5': '0.2443', 'HIT@10': '0.4291', 'NDCG@10': '0.2705', 'HIT@20': '0.5253', 'NDCG@20': '0.2948'}
{'epoch': 53, 'rec_avg_loss': '0.4909', 'joint_avg_loss': '0.7740', 'cl_avg_loss': '2.8308', 'cl_pair_0_loss': '2.8308'}
{'Epoch': 53, 'HIT@5': '0.3498', 'NDCG@5': '0.2427', 'HIT@10': '0.4285', 'NDCG@10': '0.2681', 'HIT@20': '0.5242', 'NDCG@20': '0.2923'}
{'epoch': 54, 'rec_avg_loss': '0.4925', 'joint_avg_loss': '0.7730', 'cl_avg_loss': '2.8055', 'cl_pair_0_loss': '2.8055'}
{'Epoch': 54, 'HIT@5': '0.3494', 'NDCG@5': '0.2464', 'HIT@10': '0.4312', 'NDCG@10': '0.2726', 'HIT@20': '0.5215', 'NDCG@20': '0.2954'}
{'epoch': 55, 'rec_avg_loss': '0.4873', 'joint_avg_loss': '0.7680', 'cl_avg_loss': '2.8069', 'cl_pair_0_loss': '2.8069'}
{'Epoch': 55, 'HIT@5': '0.3493', 'NDCG@5': '0.2450', 'HIT@10': '0.4329', 'NDCG@10': '0.2718', 'HIT@20': '0.5237', 'NDCG@20': '0.2947'}
{'epoch': 56, 'rec_avg_loss': '0.4856', 'joint_avg_loss': '0.7659', 'cl_avg_loss': '2.8031', 'cl_pair_0_loss': '2.8031'}
{'Epoch': 56, 'HIT@5': '0.3510', 'NDCG@5': '0.2449', 'HIT@10': '0.4352', 'NDCG@10': '0.2719', 'HIT@20': '0.5263', 'NDCG@20': '0.2949'}
{'epoch': 57, 'rec_avg_loss': '0.4848', 'joint_avg_loss': '0.7625', 'cl_avg_loss': '2.7777', 'cl_pair_0_loss': '2.7777'}
{'Epoch': 57, 'HIT@5': '0.3530', 'NDCG@5': '0.2477', 'HIT@10': '0.4353', 'NDCG@10': '0.2741', 'HIT@20': '0.5253', 'NDCG@20': '0.2967'}
{'epoch': 58, 'rec_avg_loss': '0.4812', 'joint_avg_loss': '0.7582', 'cl_avg_loss': '2.7695', 'cl_pair_0_loss': '2.7695'}
{'Epoch': 58, 'HIT@5': '0.3519', 'NDCG@5': '0.2476', 'HIT@10': '0.4351', 'NDCG@10': '0.2743', 'HIT@20': '0.5271', 'NDCG@20': '0.2975'}
{'epoch': 59, 'rec_avg_loss': '0.4803', 'joint_avg_loss': '0.7583', 'cl_avg_loss': '2.7806', 'cl_pair_0_loss': '2.7806'}
{'Epoch': 59, 'HIT@5': '0.3522', 'NDCG@5': '0.2467', 'HIT@10': '0.4353', 'NDCG@10': '0.2734', 'HIT@20': '0.5268', 'NDCG@20': '0.2964'}
{'epoch': 60, 'rec_avg_loss': '0.4793', 'joint_avg_loss': '0.7569', 'cl_avg_loss': '2.7762', 'cl_pair_0_loss': '2.7762'}
{'Epoch': 60, 'HIT@5': '0.3534', 'NDCG@5': '0.2476', 'HIT@10': '0.4352', 'NDCG@10': '0.2739', 'HIT@20': '0.5282', 'NDCG@20': '0.2973'}
{'epoch': 61, 'rec_avg_loss': '0.4758', 'joint_avg_loss': '0.7511', 'cl_avg_loss': '2.7533', 'cl_pair_0_loss': '2.7533'}
{'Epoch': 61, 'HIT@5': '0.3536', 'NDCG@5': '0.2480', 'HIT@10': '0.4343', 'NDCG@10': '0.2739', 'HIT@20': '0.5271', 'NDCG@20': '0.2973'}
{'epoch': 62, 'rec_avg_loss': '0.4758', 'joint_avg_loss': '0.7515', 'cl_avg_loss': '2.7573', 'cl_pair_0_loss': '2.7573'}
{'Epoch': 62, 'HIT@5': '0.3534', 'NDCG@5': '0.2468', 'HIT@10': '0.4352', 'NDCG@10': '0.2732', 'HIT@20': '0.5278', 'NDCG@20': '0.2965'}
{'epoch': 63, 'rec_avg_loss': '0.4754', 'joint_avg_loss': '0.7500', 'cl_avg_loss': '2.7461', 'cl_pair_0_loss': '2.7461'}
{'Epoch': 63, 'HIT@5': '0.3524', 'NDCG@5': '0.2468', 'HIT@10': '0.4327', 'NDCG@10': '0.2726', 'HIT@20': '0.5295', 'NDCG@20': '0.2970'}
{'epoch': 64, 'rec_avg_loss': '0.4716', 'joint_avg_loss': '0.7458', 'cl_avg_loss': '2.7422', 'cl_pair_0_loss': '2.7422'}
{'Epoch': 64, 'HIT@5': '0.3541', 'NDCG@5': '0.2470', 'HIT@10': '0.4345', 'NDCG@10': '0.2729', 'HIT@20': '0.5289', 'NDCG@20': '0.2966'}
{'epoch': 65, 'rec_avg_loss': '0.4703', 'joint_avg_loss': '0.7428', 'cl_avg_loss': '2.7252', 'cl_pair_0_loss': '2.7252'}
{'Epoch': 65, 'HIT@5': '0.3521', 'NDCG@5': '0.2466', 'HIT@10': '0.4316', 'NDCG@10': '0.2722', 'HIT@20': '0.5296', 'NDCG@20': '0.2968'}
{'epoch': 66, 'rec_avg_loss': '0.4709', 'joint_avg_loss': '0.7420', 'cl_avg_loss': '2.7114', 'cl_pair_0_loss': '2.7114'}
{'Epoch': 66, 'HIT@5': '0.3516', 'NDCG@5': '0.2463', 'HIT@10': '0.4339', 'NDCG@10': '0.2729', 'HIT@20': '0.5300', 'NDCG@20': '0.2971'}
{'epoch': 67, 'rec_avg_loss': '0.4655', 'joint_avg_loss': '0.7358', 'cl_avg_loss': '2.7032', 'cl_pair_0_loss': '2.7032'}
{'Epoch': 67, 'HIT@5': '0.3497', 'NDCG@5': '0.2431', 'HIT@10': '0.4329', 'NDCG@10': '0.2698', 'HIT@20': '0.5313', 'NDCG@20': '0.2945'}
{'epoch': 68, 'rec_avg_loss': '0.4656', 'joint_avg_loss': '0.7378', 'cl_avg_loss': '2.7221', 'cl_pair_0_loss': '2.7221'}
{'Epoch': 68, 'HIT@5': '0.3502', 'NDCG@5': '0.2455', 'HIT@10': '0.4324', 'NDCG@10': '0.2721', 'HIT@20': '0.5328', 'NDCG@20': '0.2973'}
{'epoch': 69, 'rec_avg_loss': '0.4651', 'joint_avg_loss': '0.7383', 'cl_avg_loss': '2.7326', 'cl_pair_0_loss': '2.7326'}
{'Epoch': 69, 'HIT@5': '0.3508', 'NDCG@5': '0.2467', 'HIT@10': '0.4303', 'NDCG@10': '0.2724', 'HIT@20': '0.5312', 'NDCG@20': '0.2978'}
{'epoch': 70, 'rec_avg_loss': '0.4660', 'joint_avg_loss': '0.7366', 'cl_avg_loss': '2.7063', 'cl_pair_0_loss': '2.7063'}
{'Epoch': 70, 'HIT@5': '0.3497', 'NDCG@5': '0.2456', 'HIT@10': '0.4312', 'NDCG@10': '0.2719', 'HIT@20': '0.5324', 'NDCG@20': '0.2974'}
{'epoch': 71, 'rec_avg_loss': '0.4656', 'joint_avg_loss': '0.7374', 'cl_avg_loss': '2.7183', 'cl_pair_0_loss': '2.7183'}
{'Epoch': 71, 'HIT@5': '0.3501', 'NDCG@5': '0.2470', 'HIT@10': '0.4326', 'NDCG@10': '0.2735', 'HIT@20': '0.5317', 'NDCG@20': '0.2985'}
{'epoch': 72, 'rec_avg_loss': '0.4633', 'joint_avg_loss': '0.7321', 'cl_avg_loss': '2.6882', 'cl_pair_0_loss': '2.6882'}
{'Epoch': 72, 'HIT@5': '0.3529', 'NDCG@5': '0.2441', 'HIT@10': '0.4317', 'NDCG@10': '0.2695', 'HIT@20': '0.5344', 'NDCG@20': '0.2954'}
{'epoch': 73, 'rec_avg_loss': '0.4628', 'joint_avg_loss': '0.7327', 'cl_avg_loss': '2.6987', 'cl_pair_0_loss': '2.6987'}
{'Epoch': 73, 'HIT@5': '0.3518', 'NDCG@5': '0.2456', 'HIT@10': '0.4317', 'NDCG@10': '0.2713', 'HIT@20': '0.5318', 'NDCG@20': '0.2965'}
{'epoch': 74, 'rec_avg_loss': '0.4625', 'joint_avg_loss': '0.7322', 'cl_avg_loss': '2.6966', 'cl_pair_0_loss': '2.6966'}
{'Epoch': 74, 'HIT@5': '0.3551', 'NDCG@5': '0.2474', 'HIT@10': '0.4329', 'NDCG@10': '0.2724', 'HIT@20': '0.5329', 'NDCG@20': '0.2975'}
{'epoch': 75, 'rec_avg_loss': '0.4585', 'joint_avg_loss': '0.7277', 'cl_avg_loss': '2.6918', 'cl_pair_0_loss': '2.6918'}
{'Epoch': 75, 'HIT@5': '0.3527', 'NDCG@5': '0.2472', 'HIT@10': '0.4340', 'NDCG@10': '0.2736', 'HIT@20': '0.5342', 'NDCG@20': '0.2988'}
{'epoch': 76, 'rec_avg_loss': '0.4586', 'joint_avg_loss': '0.7271', 'cl_avg_loss': '2.6852', 'cl_pair_0_loss': '2.6852'}
{'Epoch': 76, 'HIT@5': '0.3477', 'NDCG@5': '0.2444', 'HIT@10': '0.4329', 'NDCG@10': '0.2720', 'HIT@20': '0.5349', 'NDCG@20': '0.2977'}
{'epoch': 77, 'rec_avg_loss': '0.4602', 'joint_avg_loss': '0.7278', 'cl_avg_loss': '2.6762', 'cl_pair_0_loss': '2.6762'}
{'Epoch': 77, 'HIT@5': '0.3555', 'NDCG@5': '0.2470', 'HIT@10': '0.4336', 'NDCG@10': '0.2721', 'HIT@20': '0.5362', 'NDCG@20': '0.2980'}
{'epoch': 78, 'rec_avg_loss': '0.4573', 'joint_avg_loss': '0.7224', 'cl_avg_loss': '2.6512', 'cl_pair_0_loss': '2.6512'}
{'Epoch': 78, 'HIT@5': '0.3480', 'NDCG@5': '0.2437', 'HIT@10': '0.4314', 'NDCG@10': '0.2707', 'HIT@20': '0.5358', 'NDCG@20': '0.2971'}
{'epoch': 79, 'rec_avg_loss': '0.4580', 'joint_avg_loss': '0.7260', 'cl_avg_loss': '2.6792', 'cl_pair_0_loss': '2.6792'}
{'Epoch': 79, 'HIT@5': '0.3532', 'NDCG@5': '0.2461', 'HIT@10': '0.4330', 'NDCG@10': '0.2719', 'HIT@20': '0.5372', 'NDCG@20': '0.2980'}
{'epoch': 80, 'rec_avg_loss': '0.4550', 'joint_avg_loss': '0.7230', 'cl_avg_loss': '2.6800', 'cl_pair_0_loss': '2.6800'}
{'Epoch': 80, 'HIT@5': '0.3466', 'NDCG@5': '0.2441', 'HIT@10': '0.4321', 'NDCG@10': '0.2718', 'HIT@20': '0.5355', 'NDCG@20': '0.2978'}
{'epoch': 81, 'rec_avg_loss': '0.4548', 'joint_avg_loss': '0.7197', 'cl_avg_loss': '2.6494', 'cl_pair_0_loss': '2.6494'}
{'Epoch': 81, 'HIT@5': '0.3510', 'NDCG@5': '0.2440', 'HIT@10': '0.4325', 'NDCG@10': '0.2703', 'HIT@20': '0.5372', 'NDCG@20': '0.2967'}
{'epoch': 82, 'rec_avg_loss': '0.4568', 'joint_avg_loss': '0.7212', 'cl_avg_loss': '2.6443', 'cl_pair_0_loss': '2.6443'}
{'Epoch': 82, 'HIT@5': '0.3511', 'NDCG@5': '0.2450', 'HIT@10': '0.4340', 'NDCG@10': '0.2717', 'HIT@20': '0.5362', 'NDCG@20': '0.2974'}
{'epoch': 83, 'rec_avg_loss': '0.4543', 'joint_avg_loss': '0.7195', 'cl_avg_loss': '2.6522', 'cl_pair_0_loss': '2.6522'}
{'Epoch': 83, 'HIT@5': '0.3500', 'NDCG@5': '0.2426', 'HIT@10': '0.4342', 'NDCG@10': '0.2697', 'HIT@20': '0.5377', 'NDCG@20': '0.2957'}
{'epoch': 84, 'rec_avg_loss': '0.4509', 'joint_avg_loss': '0.7155', 'cl_avg_loss': '2.6467', 'cl_pair_0_loss': '2.6467'}
{'Epoch': 84, 'HIT@5': '0.3500', 'NDCG@5': '0.2428', 'HIT@10': '0.4339', 'NDCG@10': '0.2699', 'HIT@20': '0.5348', 'NDCG@20': '0.2953'}
{'epoch': 85, 'rec_avg_loss': '0.4513', 'joint_avg_loss': '0.7171', 'cl_avg_loss': '2.6576', 'cl_pair_0_loss': '2.6576'}
{'Epoch': 85, 'HIT@5': '0.3510', 'NDCG@5': '0.2408', 'HIT@10': '0.4341', 'NDCG@10': '0.2676', 'HIT@20': '0.5372', 'NDCG@20': '0.2936'}
{'epoch': 86, 'rec_avg_loss': '0.4529', 'joint_avg_loss': '0.7164', 'cl_avg_loss': '2.6352', 'cl_pair_0_loss': '2.6352'}
{'Epoch': 86, 'HIT@5': '0.3516', 'NDCG@5': '0.2428', 'HIT@10': '0.4349', 'NDCG@10': '0.2697', 'HIT@20': '0.5385', 'NDCG@20': '0.2958'}
{'epoch': 87, 'rec_avg_loss': '0.4503', 'joint_avg_loss': '0.7138', 'cl_avg_loss': '2.6347', 'cl_pair_0_loss': '2.6347'}
{'Epoch': 87, 'HIT@5': '0.3476', 'NDCG@5': '0.2412', 'HIT@10': '0.4331', 'NDCG@10': '0.2687', 'HIT@20': '0.5370', 'NDCG@20': '0.2949'}
{'epoch': 88, 'rec_avg_loss': '0.4505', 'joint_avg_loss': '0.7145', 'cl_avg_loss': '2.6400', 'cl_pair_0_loss': '2.6400'}
{'Epoch': 88, 'HIT@5': '0.3481', 'NDCG@5': '0.2409', 'HIT@10': '0.4347', 'NDCG@10': '0.2688', 'HIT@20': '0.5362', 'NDCG@20': '0.2944'}
{'epoch': 89, 'rec_avg_loss': '0.4485', 'joint_avg_loss': '0.7100', 'cl_avg_loss': '2.6155', 'cl_pair_0_loss': '2.6155'}
{'Epoch': 89, 'HIT@5': '0.3484', 'NDCG@5': '0.2414', 'HIT@10': '0.4343', 'NDCG@10': '0.2690', 'HIT@20': '0.5383', 'NDCG@20': '0.2952'}
{'epoch': 90, 'rec_avg_loss': '0.4480', 'joint_avg_loss': '0.7120', 'cl_avg_loss': '2.6399', 'cl_pair_0_loss': '2.6399'}
{'Epoch': 90, 'HIT@5': '0.3479', 'NDCG@5': '0.2411', 'HIT@10': '0.4354', 'NDCG@10': '0.2692', 'HIT@20': '0.5377', 'NDCG@20': '0.2951'}
{'epoch': 91, 'rec_avg_loss': '0.4490', 'joint_avg_loss': '0.7125', 'cl_avg_loss': '2.6347', 'cl_pair_0_loss': '2.6347'}
{'Epoch': 91, 'HIT@5': '0.3496', 'NDCG@5': '0.2420', 'HIT@10': '0.4352', 'NDCG@10': '0.2696', 'HIT@20': '0.5392', 'NDCG@20': '0.2958'}
{'epoch': 92, 'rec_avg_loss': '0.4489', 'joint_avg_loss': '0.7078', 'cl_avg_loss': '2.5891', 'cl_pair_0_loss': '2.5891'}
{'Epoch': 92, 'HIT@5': '0.3464', 'NDCG@5': '0.2395', 'HIT@10': '0.4337', 'NDCG@10': '0.2677', 'HIT@20': '0.5365', 'NDCG@20': '0.2936'}
{'epoch': 93, 'rec_avg_loss': '0.4439', 'joint_avg_loss': '0.7061', 'cl_avg_loss': '2.6216', 'cl_pair_0_loss': '2.6216'}
{'Epoch': 93, 'HIT@5': '0.3496', 'NDCG@5': '0.2427', 'HIT@10': '0.4368', 'NDCG@10': '0.2708', 'HIT@20': '0.5412', 'NDCG@20': '0.2971'}
{'epoch': 94, 'rec_avg_loss': '0.4469', 'joint_avg_loss': '0.7074', 'cl_avg_loss': '2.6056', 'cl_pair_0_loss': '2.6056'}
{'Epoch': 94, 'HIT@5': '0.3485', 'NDCG@5': '0.2420', 'HIT@10': '0.4349', 'NDCG@10': '0.2698', 'HIT@20': '0.5402', 'NDCG@20': '0.2963'}
{'epoch': 95, 'rec_avg_loss': '0.4489', 'joint_avg_loss': '0.7081', 'cl_avg_loss': '2.5921', 'cl_pair_0_loss': '2.5921'}
{'Epoch': 95, 'HIT@5': '0.3456', 'NDCG@5': '0.2378', 'HIT@10': '0.4337', 'NDCG@10': '0.2661', 'HIT@20': '0.5382', 'NDCG@20': '0.2925'}
{'epoch': 96, 'rec_avg_loss': '0.4458', 'joint_avg_loss': '0.7054', 'cl_avg_loss': '2.5961', 'cl_pair_0_loss': '2.5961'}
{'Epoch': 96, 'HIT@5': '0.3474', 'NDCG@5': '0.2388', 'HIT@10': '0.4361', 'NDCG@10': '0.2674', 'HIT@20': '0.5398', 'NDCG@20': '0.2935'}
{'epoch': 97, 'rec_avg_loss': '0.4472', 'joint_avg_loss': '0.7073', 'cl_avg_loss': '2.6003', 'cl_pair_0_loss': '2.6003'}
{'Epoch': 97, 'HIT@5': '0.3505', 'NDCG@5': '0.2446', 'HIT@10': '0.4378', 'NDCG@10': '0.2727', 'HIT@20': '0.5416', 'NDCG@20': '0.2989'}
{'epoch': 98, 'rec_avg_loss': '0.4426', 'joint_avg_loss': '0.7034', 'cl_avg_loss': '2.6080', 'cl_pair_0_loss': '2.6080'}
{'Epoch': 98, 'HIT@5': '0.3484', 'NDCG@5': '0.2391', 'HIT@10': '0.4369', 'NDCG@10': '0.2677', 'HIT@20': '0.5399', 'NDCG@20': '0.2937'}
{'epoch': 99, 'rec_avg_loss': '0.4463', 'joint_avg_loss': '0.7051', 'cl_avg_loss': '2.5882', 'cl_pair_0_loss': '2.5882'}
{'Epoch': 99, 'HIT@5': '0.3486', 'NDCG@5': '0.2402', 'HIT@10': '0.4352', 'NDCG@10': '0.2681', 'HIT@20': '0.5401', 'NDCG@20': '0.2946'}
{'Epoch': 0, 'HIT@5': '0.3285', 'NDCG@5': '0.2758', 'HIT@10': '0.4138', 'NDCG@10': '0.3035', 'HIT@20': '0.5084', 'NDCG@20': '0.3273'}
CoSeRec-mooc-0
{'Epoch': 0, 'HIT@5': '0.3285', 'NDCG@5': '0.2758', 'HIT@10': '0.4138', 'NDCG@10': '0.3035', 'HIT@20': '0.5084', 'NDCG@20': '0.3273'}
Namespace(data_dir='../data/', output_dir='output/', data_name='mooc', do_eval=False, model_idx=0, gpu_id='0', noise_ratio=0.0, training_data_ratio=1.0, augment_threshold=4, similarity_model_name='ItemCF_IUF', augmentation_warm_up_epoches=300, base_augment_type='random', augment_type_for_short='SIM', tao=0.2, gamma=0.7, beta=0.2, substitute_rate=0.1, insert_rate=0.4, max_insert_num_per_pos=1, temperature=1.0, n_views=2, model_name='CoSeRec', hidden_size=64, num_hidden_layers=2, num_attention_heads=2, hidden_act='gelu', attention_probs_dropout_prob=0.5, hidden_dropout_prob=0.5, initializer_range=0.02, max_seq_length=50, lr=0.001, batch_size=256, epochs=100, no_cuda=False, log_freq=1, seed=1, cf_weight=0.1, rec_weight=1.0, weight_decay=0.0, adam_beta1=0.9, adam_beta2=0.999, device_type='mps', cuda_condition=False, data_file='../data/mooc.txt', item_size=1304, mask_id=1303, log_file='output/CoSeRec-mooc-0.txt')
{'epoch': 0, 'rec_avg_loss': '1.0603', 'joint_avg_loss': '4.5332', 'cl_avg_loss': '34.7282', 'cl_pair_0_loss': '34.7282'}
{'Epoch': 0, 'HIT@5': '0.0661', 'NDCG@5': '0.0403', 'HIT@10': '0.1124', 'NDCG@10': '0.0551', 'HIT@20': '0.1995', 'NDCG@20': '0.0769'}
{'epoch': 1, 'rec_avg_loss': '0.8501', 'joint_avg_loss': '2.2230', 'cl_avg_loss': '13.7293', 'cl_pair_0_loss': '13.7293'}
{'Epoch': 1, 'HIT@5': '0.0874', 'NDCG@5': '0.0528', 'HIT@10': '0.1454', 'NDCG@10': '0.0716', 'HIT@20': '0.2169', 'NDCG@20': '0.0896'}
{'epoch': 2, 'rec_avg_loss': '0.7881', 'joint_avg_loss': '1.6196', 'cl_avg_loss': '8.3148', 'cl_pair_0_loss': '8.3148'}
{'Epoch': 2, 'HIT@5': '0.0941', 'NDCG@5': '0.0606', 'HIT@10': '0.1554', 'NDCG@10': '0.0804', 'HIT@20': '0.2275', 'NDCG@20': '0.0985'}
{'epoch': 3, 'rec_avg_loss': '0.7528', 'joint_avg_loss': '1.3901', 'cl_avg_loss': '6.3731', 'cl_pair_0_loss': '6.3731'}
{'Epoch': 3, 'HIT@5': '0.1159', 'NDCG@5': '0.0748', 'HIT@10': '0.1664', 'NDCG@10': '0.0913', 'HIT@20': '0.2323', 'NDCG@20': '0.1079'}
{'epoch': 4, 'rec_avg_loss': '0.7291', 'joint_avg_loss': '1.2680', 'cl_avg_loss': '5.3889', 'cl_pair_0_loss': '5.3889'}
{'Epoch': 4, 'HIT@5': '0.1225', 'NDCG@5': '0.0821', 'HIT@10': '0.1753', 'NDCG@10': '0.0994', 'HIT@20': '0.3459', 'NDCG@20': '0.1404'}
{'epoch': 5, 'rec_avg_loss': '0.7136', 'joint_avg_loss': '1.1889', 'cl_avg_loss': '4.7530', 'cl_pair_0_loss': '4.7530'}
{'Epoch': 5, 'HIT@5': '0.1347', 'NDCG@5': '0.0908', 'HIT@10': '0.1859', 'NDCG@10': '0.1073', 'HIT@20': '0.2555', 'NDCG@20': '0.1249'}
{'epoch': 6, 'rec_avg_loss': '0.7008', 'joint_avg_loss': '1.1346', 'cl_avg_loss': '4.3378', 'cl_pair_0_loss': '4.3378'}
{'Epoch': 6, 'HIT@5': '0.1398', 'NDCG@5': '0.0972', 'HIT@10': '0.1943', 'NDCG@10': '0.1147', 'HIT@20': '0.3561', 'NDCG@20': '0.1540'}
{'epoch': 7, 'rec_avg_loss': '0.6894', 'joint_avg_loss': '1.0964', 'cl_avg_loss': '4.0706', 'cl_pair_0_loss': '4.0706'}
{'Epoch': 7, 'HIT@5': '0.1420', 'NDCG@5': '0.0989', 'HIT@10': '0.1944', 'NDCG@10': '0.1159', 'HIT@20': '0.3582', 'NDCG@20': '0.1564'}
{'epoch': 8, 'rec_avg_loss': '0.6845', 'joint_avg_loss': '1.0641', 'cl_avg_loss': '3.7956', 'cl_pair_0_loss': '3.7956'}
{'Epoch': 8, 'HIT@5': '0.1399', 'NDCG@5': '0.0992', 'HIT@10': '0.1946', 'NDCG@10': '0.1168', 'HIT@20': '0.3607', 'NDCG@20': '0.1596'}
{'epoch': 9, 'rec_avg_loss': '0.6794', 'joint_avg_loss': '1.0450', 'cl_avg_loss': '3.6566', 'cl_pair_0_loss': '3.6566'}
{'Epoch': 9, 'HIT@5': '0.1417', 'NDCG@5': '0.1011', 'HIT@10': '0.1973', 'NDCG@10': '0.1191', 'HIT@20': '0.3640', 'NDCG@20': '0.1624'}
{'epoch': 10, 'rec_avg_loss': '0.6764', 'joint_avg_loss': '1.0297', 'cl_avg_loss': '3.5330', 'cl_pair_0_loss': '3.5330'}
{'Epoch': 10, 'HIT@5': '0.1475', 'NDCG@5': '0.1046', 'HIT@10': '0.2549', 'NDCG@10': '0.1379', 'HIT@20': '0.3663', 'NDCG@20': '0.1661'}
{'epoch': 11, 'rec_avg_loss': '0.6691', 'joint_avg_loss': '1.0150', 'cl_avg_loss': '3.4598', 'cl_pair_0_loss': '3.4598'}
{'Epoch': 11, 'HIT@5': '0.1475', 'NDCG@5': '0.1047', 'HIT@10': '0.2608', 'NDCG@10': '0.1412', 'HIT@20': '0.3713', 'NDCG@20': '0.1693'}
{'epoch': 12, 'rec_avg_loss': '0.6673', 'joint_avg_loss': '1.0036', 'cl_avg_loss': '3.3627', 'cl_pair_0_loss': '3.3627'}
{'Epoch': 12, 'HIT@5': '0.1484', 'NDCG@5': '0.1074', 'HIT@10': '0.2653', 'NDCG@10': '0.1436', 'HIT@20': '0.3781', 'NDCG@20': '0.1720'}
{'epoch': 13, 'rec_avg_loss': '0.6621', 'joint_avg_loss': '0.9891', 'cl_avg_loss': '3.2705', 'cl_pair_0_loss': '3.2705'}
{'Epoch': 13, 'HIT@5': '0.1503', 'NDCG@5': '0.1087', 'HIT@10': '0.2625', 'NDCG@10': '0.1441', 'HIT@20': '0.3815', 'NDCG@20': '0.1744'}
{'epoch': 14, 'rec_avg_loss': '0.6552', 'joint_avg_loss': '0.9783', 'cl_avg_loss': '3.2306', 'cl_pair_0_loss': '3.2306'}
{'Epoch': 14, 'HIT@5': '0.1512', 'NDCG@5': '0.1110', 'HIT@10': '0.2656', 'NDCG@10': '0.1473', 'HIT@20': '0.3902', 'NDCG@20': '0.1790'}
{'epoch': 15, 'rec_avg_loss': '0.6514', 'joint_avg_loss': '0.9677', 'cl_avg_loss': '3.1632', 'cl_pair_0_loss': '3.1632'}
{'Epoch': 15, 'HIT@5': '0.1564', 'NDCG@5': '0.1108', 'HIT@10': '0.2709', 'NDCG@10': '0.1473', 'HIT@20': '0.3966', 'NDCG@20': '0.1799'}
{'epoch': 16, 'rec_avg_loss': '0.6482', 'joint_avg_loss': '0.9618', 'cl_avg_loss': '3.1358', 'cl_pair_0_loss': '3.1358'}
{'Epoch': 16, 'HIT@5': '0.1563', 'NDCG@5': '0.1120', 'HIT@10': '0.2663', 'NDCG@10': '0.1467', 'HIT@20': '0.3937', 'NDCG@20': '0.1792'}
{'epoch': 17, 'rec_avg_loss': '0.6458', 'joint_avg_loss': '0.9572', 'cl_avg_loss': '3.1138', 'cl_pair_0_loss': '3.1138'}
{'Epoch': 17, 'HIT@5': '0.1618', 'NDCG@5': '0.1145', 'HIT@10': '0.2711', 'NDCG@10': '0.1493', 'HIT@20': '0.4083', 'NDCG@20': '0.1843'}
{'epoch': 18, 'rec_avg_loss': '0.6389', 'joint_avg_loss': '0.9473', 'cl_avg_loss': '3.0836', 'cl_pair_0_loss': '3.0836'}
{'Epoch': 18, 'HIT@5': '0.1602', 'NDCG@5': '0.1158', 'HIT@10': '0.2768', 'NDCG@10': '0.1525', 'HIT@20': '0.4142', 'NDCG@20': '0.1881'}
{'epoch': 19, 'rec_avg_loss': '0.6357', 'joint_avg_loss': '0.9400', 'cl_avg_loss': '3.0435', 'cl_pair_0_loss': '3.0435'}
{'Epoch': 19, 'HIT@5': '0.1651', 'NDCG@5': '0.1203', 'HIT@10': '0.2813', 'NDCG@10': '0.1571', 'HIT@20': '0.4172', 'NDCG@20': '0.1918'}
{'epoch': 20, 'rec_avg_loss': '0.6295', 'joint_avg_loss': '0.9298', 'cl_avg_loss': '3.0023', 'cl_pair_0_loss': '3.0023'}
{'Epoch': 20, 'HIT@5': '0.2193', 'NDCG@5': '0.1417', 'HIT@10': '0.3273', 'NDCG@10': '0.1755', 'HIT@20': '0.4268', 'NDCG@20': '0.2004'}
{'epoch': 21, 'rec_avg_loss': '0.6251', 'joint_avg_loss': '0.9262', 'cl_avg_loss': '3.0103', 'cl_pair_0_loss': '3.0103'}
{'Epoch': 21, 'HIT@5': '0.2243', 'NDCG@5': '0.1462', 'HIT@10': '0.3315', 'NDCG@10': '0.1810', 'HIT@20': '0.4296', 'NDCG@20': '0.2055'}
{'epoch': 22, 'rec_avg_loss': '0.6180', 'joint_avg_loss': '0.9166', 'cl_avg_loss': '2.9859', 'cl_pair_0_loss': '2.9859'}
{'Epoch': 22, 'HIT@5': '0.2300', 'NDCG@5': '0.1523', 'HIT@10': '0.3372', 'NDCG@10': '0.1873', 'HIT@20': '0.4403', 'NDCG@20': '0.2133'}
{'epoch': 23, 'rec_avg_loss': '0.6119', 'joint_avg_loss': '0.9126', 'cl_avg_loss': '3.0072', 'cl_pair_0_loss': '3.0072'}
{'Epoch': 23, 'HIT@5': '0.2713', 'NDCG@5': '0.1781', 'HIT@10': '0.3416', 'NDCG@10': '0.2010', 'HIT@20': '0.4444', 'NDCG@20': '0.2269'}
{'epoch': 24, 'rec_avg_loss': '0.6074', 'joint_avg_loss': '0.9033', 'cl_avg_loss': '2.9597', 'cl_pair_0_loss': '2.9597'}
{'Epoch': 24, 'HIT@5': '0.2377', 'NDCG@5': '0.1627', 'HIT@10': '0.3482', 'NDCG@10': '0.1996', 'HIT@20': '0.4492', 'NDCG@20': '0.2251'}
{'epoch': 25, 'rec_avg_loss': '0.6023', 'joint_avg_loss': '0.8993', 'cl_avg_loss': '2.9701', 'cl_pair_0_loss': '2.9701'}
{'Epoch': 25, 'HIT@5': '0.2721', 'NDCG@5': '0.1737', 'HIT@10': '0.3459', 'NDCG@10': '0.1976', 'HIT@20': '0.4539', 'NDCG@20': '0.2248'}
{'epoch': 26, 'rec_avg_loss': '0.5939', 'joint_avg_loss': '0.8890', 'cl_avg_loss': '2.9519', 'cl_pair_0_loss': '2.9519'}
{'Epoch': 26, 'HIT@5': '0.2864', 'NDCG@5': '0.1876', 'HIT@10': '0.3576', 'NDCG@10': '0.2106', 'HIT@20': '0.4621', 'NDCG@20': '0.2371'}
{'epoch': 27, 'rec_avg_loss': '0.5913', 'joint_avg_loss': '0.8858', 'cl_avg_loss': '2.9446', 'cl_pair_0_loss': '2.9446'}
{'Epoch': 27, 'HIT@5': '0.2902', 'NDCG@5': '0.1899', 'HIT@10': '0.3645', 'NDCG@10': '0.2138', 'HIT@20': '0.4635', 'NDCG@20': '0.2388'}
{'epoch': 28, 'rec_avg_loss': '0.5847', 'joint_avg_loss': '0.8798', 'cl_avg_loss': '2.9512', 'cl_pair_0_loss': '2.9512'}
{'Epoch': 28, 'HIT@5': '0.2929', 'NDCG@5': '0.1967', 'HIT@10': '0.3707', 'NDCG@10': '0.2218', 'HIT@20': '0.4732', 'NDCG@20': '0.2477'}
{'epoch': 29, 'rec_avg_loss': '0.5825', 'joint_avg_loss': '0.8768', 'cl_avg_loss': '2.9430', 'cl_pair_0_loss': '2.9430'}
{'Epoch': 29, 'HIT@5': '0.3015', 'NDCG@5': '0.1996', 'HIT@10': '0.3787', 'NDCG@10': '0.2244', 'HIT@20': '0.4759', 'NDCG@20': '0.2490'}
{'epoch': 30, 'rec_avg_loss': '0.5761', 'joint_avg_loss': '0.8710', 'cl_avg_loss': '2.9492', 'cl_pair_0_loss': '2.9492'}
{'Epoch': 30, 'HIT@5': '0.3037', 'NDCG@5': '0.2096', 'HIT@10': '0.3799', 'NDCG@10': '0.2341', 'HIT@20': '0.4776', 'NDCG@20': '0.2588'}
{'epoch': 31, 'rec_avg_loss': '0.5722', 'joint_avg_loss': '0.8636', 'cl_avg_loss': '2.9142', 'cl_pair_0_loss': '2.9142'}
{'Epoch': 31, 'HIT@5': '0.3099', 'NDCG@5': '0.2154', 'HIT@10': '0.3871', 'NDCG@10': '0.2402', 'HIT@20': '0.4820', 'NDCG@20': '0.2641'}
{'epoch': 32, 'rec_avg_loss': '0.5657', 'joint_avg_loss': '0.8575', 'cl_avg_loss': '2.9186', 'cl_pair_0_loss': '2.9186'}
{'Epoch': 32, 'HIT@5': '0.3107', 'NDCG@5': '0.2164', 'HIT@10': '0.3918', 'NDCG@10': '0.2424', 'HIT@20': '0.4834', 'NDCG@20': '0.2655'}
{'epoch': 33, 'rec_avg_loss': '0.5655', 'joint_avg_loss': '0.8574', 'cl_avg_loss': '2.9185', 'cl_pair_0_loss': '2.9185'}
{'Epoch': 33, 'HIT@5': '0.3153', 'NDCG@5': '0.2194', 'HIT@10': '0.3975', 'NDCG@10': '0.2458', 'HIT@20': '0.4881', 'NDCG@20': '0.2686'}
{'epoch': 34, 'rec_avg_loss': '0.5587', 'joint_avg_loss': '0.8477', 'cl_avg_loss': '2.8897', 'cl_pair_0_loss': '2.8897'}
{'Epoch': 34, 'HIT@5': '0.3162', 'NDCG@5': '0.2189', 'HIT@10': '0.3966', 'NDCG@10': '0.2448', 'HIT@20': '0.4911', 'NDCG@20': '0.2688'}
{'epoch': 35, 'rec_avg_loss': '0.5534', 'joint_avg_loss': '0.8457', 'cl_avg_loss': '2.9226', 'cl_pair_0_loss': '2.9226'}
{'Epoch': 35, 'HIT@5': '0.3227', 'NDCG@5': '0.2257', 'HIT@10': '0.4011', 'NDCG@10': '0.2511', 'HIT@20': '0.4955', 'NDCG@20': '0.2750'}
{'epoch': 36, 'rec_avg_loss': '0.5488', 'joint_avg_loss': '0.8373', 'cl_avg_loss': '2.8844', 'cl_pair_0_loss': '2.8844'}
{'Epoch': 36, 'HIT@5': '0.3296', 'NDCG@5': '0.2283', 'HIT@10': '0.4105', 'NDCG@10': '0.2545', 'HIT@20': '0.5048', 'NDCG@20': '0.2783'}
{'epoch': 37, 'rec_avg_loss': '0.5429', 'joint_avg_loss': '0.8336', 'cl_avg_loss': '2.9066', 'cl_pair_0_loss': '2.9066'}
{'Epoch': 37, 'HIT@5': '0.3306', 'NDCG@5': '0.2292', 'HIT@10': '0.4118', 'NDCG@10': '0.2556', 'HIT@20': '0.5023', 'NDCG@20': '0.2784'}
{'epoch': 38, 'rec_avg_loss': '0.5411', 'joint_avg_loss': '0.8309', 'cl_avg_loss': '2.8976', 'cl_pair_0_loss': '2.8976'}
{'Epoch': 38, 'HIT@5': '0.3336', 'NDCG@5': '0.2334', 'HIT@10': '0.4119', 'NDCG@10': '0.2587', 'HIT@20': '0.5053', 'NDCG@20': '0.2823'}
{'epoch': 39, 'rec_avg_loss': '0.5373', 'joint_avg_loss': '0.8274', 'cl_avg_loss': '2.9010', 'cl_pair_0_loss': '2.9010'}
{'Epoch': 39, 'HIT@5': '0.3296', 'NDCG@5': '0.2285', 'HIT@10': '0.4122', 'NDCG@10': '0.2553', 'HIT@20': '0.5061', 'NDCG@20': '0.2790'}
{'epoch': 40, 'rec_avg_loss': '0.5331', 'joint_avg_loss': '0.8203', 'cl_avg_loss': '2.8724', 'cl_pair_0_loss': '2.8724'}
{'Epoch': 40, 'HIT@5': '0.3378', 'NDCG@5': '0.2349', 'HIT@10': '0.4157', 'NDCG@10': '0.2600', 'HIT@20': '0.5103', 'NDCG@20': '0.2838'}
{'epoch': 41, 'rec_avg_loss': '0.5277', 'joint_avg_loss': '0.8171', 'cl_avg_loss': '2.8945', 'cl_pair_0_loss': '2.8945'}
{'Epoch': 41, 'HIT@5': '0.3404', 'NDCG@5': '0.2369', 'HIT@10': '0.4153', 'NDCG@10': '0.2610', 'HIT@20': '0.5105', 'NDCG@20': '0.2850'}
{'epoch': 42, 'rec_avg_loss': '0.5250', 'joint_avg_loss': '0.8142', 'cl_avg_loss': '2.8926', 'cl_pair_0_loss': '2.8926'}
{'Epoch': 42, 'HIT@5': '0.3447', 'NDCG@5': '0.2397', 'HIT@10': '0.4209', 'NDCG@10': '0.2643', 'HIT@20': '0.5149', 'NDCG@20': '0.2880'}
{'epoch': 43, 'rec_avg_loss': '0.5220', 'joint_avg_loss': '0.8103', 'cl_avg_loss': '2.8833', 'cl_pair_0_loss': '2.8833'}
{'Epoch': 43, 'HIT@5': '0.3468', 'NDCG@5': '0.2418', 'HIT@10': '0.4226', 'NDCG@10': '0.2663', 'HIT@20': '0.5172', 'NDCG@20': '0.2902'}
{'epoch': 44, 'rec_avg_loss': '0.5197', 'joint_avg_loss': '0.8041', 'cl_avg_loss': '2.8449', 'cl_pair_0_loss': '2.8449'}
{'Epoch': 44, 'HIT@5': '0.3460', 'NDCG@5': '0.2409', 'HIT@10': '0.4210', 'NDCG@10': '0.2650', 'HIT@20': '0.5153', 'NDCG@20': '0.2888'}
{'epoch': 45, 'rec_avg_loss': '0.5119', 'joint_avg_loss': '0.7985', 'cl_avg_loss': '2.8658', 'cl_pair_0_loss': '2.8658'}
{'Epoch': 45, 'HIT@5': '0.3437', 'NDCG@5': '0.2391', 'HIT@10': '0.4201', 'NDCG@10': '0.2637', 'HIT@20': '0.5174', 'NDCG@20': '0.2883'}
{'epoch': 46, 'rec_avg_loss': '0.5103', 'joint_avg_loss': '0.7951', 'cl_avg_loss': '2.8487', 'cl_pair_0_loss': '2.8487'}
{'Epoch': 46, 'HIT@5': '0.3433', 'NDCG@5': '0.2380', 'HIT@10': '0.4211', 'NDCG@10': '0.2632', 'HIT@20': '0.5181', 'NDCG@20': '0.2877'}
{'epoch': 47, 'rec_avg_loss': '0.5068', 'joint_avg_loss': '0.7905', 'cl_avg_loss': '2.8371', 'cl_pair_0_loss': '2.8371'}
{'Epoch': 47, 'HIT@5': '0.3406', 'NDCG@5': '0.2367', 'HIT@10': '0.4229', 'NDCG@10': '0.2632', 'HIT@20': '0.5219', 'NDCG@20': '0.2883'}
{'epoch': 48, 'rec_avg_loss': '0.5055', 'joint_avg_loss': '0.7920', 'cl_avg_loss': '2.8645', 'cl_pair_0_loss': '2.8645'}
{'Epoch': 48, 'HIT@5': '0.3456', 'NDCG@5': '0.2410', 'HIT@10': '0.4270', 'NDCG@10': '0.2672', 'HIT@20': '0.5239', 'NDCG@20': '0.2916'}
{'epoch': 49, 'rec_avg_loss': '0.5002', 'joint_avg_loss': '0.7839', 'cl_avg_loss': '2.8368', 'cl_pair_0_loss': '2.8368'}
{'Epoch': 49, 'HIT@5': '0.3471', 'NDCG@5': '0.2442', 'HIT@10': '0.4256', 'NDCG@10': '0.2695', 'HIT@20': '0.5246', 'NDCG@20': '0.2945'}
{'epoch': 50, 'rec_avg_loss': '0.4985', 'joint_avg_loss': '0.7804', 'cl_avg_loss': '2.8196', 'cl_pair_0_loss': '2.8196'}
{'Epoch': 50, 'HIT@5': '0.3487', 'NDCG@5': '0.2436', 'HIT@10': '0.4259', 'NDCG@10': '0.2685', 'HIT@20': '0.5246', 'NDCG@20': '0.2933'}
{'epoch': 51, 'rec_avg_loss': '0.4958', 'joint_avg_loss': '0.7768', 'cl_avg_loss': '2.8099', 'cl_pair_0_loss': '2.8099'}
{'Epoch': 51, 'HIT@5': '0.3481', 'NDCG@5': '0.2438', 'HIT@10': '0.4285', 'NDCG@10': '0.2698', 'HIT@20': '0.5244', 'NDCG@20': '0.2940'}
{'epoch': 52, 'rec_avg_loss': '0.4942', 'joint_avg_loss': '0.7776', 'cl_avg_loss': '2.8343', 'cl_pair_0_loss': '2.8343'}
{'Epoch': 52, 'HIT@5': '0.3483', 'NDCG@5': '0.2443', 'HIT@10': '0.4291', 'NDCG@10': '0.2704', 'HIT@20': '0.5253', 'NDCG@20': '0.2948'}
{'epoch': 53, 'rec_avg_loss': '0.4909', 'joint_avg_loss': '0.7740', 'cl_avg_loss': '2.8308', 'cl_pair_0_loss': '2.8308'}
{'Epoch': 53, 'HIT@5': '0.3498', 'NDCG@5': '0.2427', 'HIT@10': '0.4285', 'NDCG@10': '0.2681', 'HIT@20': '0.5241', 'NDCG@20': '0.2923'}
{'epoch': 54, 'rec_avg_loss': '0.4925', 'joint_avg_loss': '0.7730', 'cl_avg_loss': '2.8055', 'cl_pair_0_loss': '2.8055'}
{'Epoch': 54, 'HIT@5': '0.3494', 'NDCG@5': '0.2464', 'HIT@10': '0.4312', 'NDCG@10': '0.2726', 'HIT@20': '0.5216', 'NDCG@20': '0.2954'}
{'epoch': 55, 'rec_avg_loss': '0.4873', 'joint_avg_loss': '0.7680', 'cl_avg_loss': '2.8069', 'cl_pair_0_loss': '2.8069'}
{'Epoch': 55, 'HIT@5': '0.3493', 'NDCG@5': '0.2450', 'HIT@10': '0.4330', 'NDCG@10': '0.2718', 'HIT@20': '0.5237', 'NDCG@20': '0.2947'}
{'epoch': 56, 'rec_avg_loss': '0.4856', 'joint_avg_loss': '0.7659', 'cl_avg_loss': '2.8031', 'cl_pair_0_loss': '2.8031'}
{'Epoch': 56, 'HIT@5': '0.3510', 'NDCG@5': '0.2449', 'HIT@10': '0.4352', 'NDCG@10': '0.2719', 'HIT@20': '0.5262', 'NDCG@20': '0.2948'}
{'epoch': 57, 'rec_avg_loss': '0.4848', 'joint_avg_loss': '0.7625', 'cl_avg_loss': '2.7777', 'cl_pair_0_loss': '2.7777'}
{'Epoch': 57, 'HIT@5': '0.3530', 'NDCG@5': '0.2476', 'HIT@10': '0.4353', 'NDCG@10': '0.2741', 'HIT@20': '0.5253', 'NDCG@20': '0.2967'}
{'epoch': 58, 'rec_avg_loss': '0.4812', 'joint_avg_loss': '0.7582', 'cl_avg_loss': '2.7695', 'cl_pair_0_loss': '2.7695'}
{'Epoch': 58, 'HIT@5': '0.3519', 'NDCG@5': '0.2476', 'HIT@10': '0.4351', 'NDCG@10': '0.2743', 'HIT@20': '0.5271', 'NDCG@20': '0.2975'}
{'epoch': 59, 'rec_avg_loss': '0.4803', 'joint_avg_loss': '0.7583', 'cl_avg_loss': '2.7806', 'cl_pair_0_loss': '2.7806'}
{'Epoch': 59, 'HIT@5': '0.3523', 'NDCG@5': '0.2467', 'HIT@10': '0.4353', 'NDCG@10': '0.2734', 'HIT@20': '0.5268', 'NDCG@20': '0.2964'}
{'epoch': 60, 'rec_avg_loss': '0.4793', 'joint_avg_loss': '0.7569', 'cl_avg_loss': '2.7762', 'cl_pair_0_loss': '2.7762'}
{'Epoch': 60, 'HIT@5': '0.3534', 'NDCG@5': '0.2476', 'HIT@10': '0.4352', 'NDCG@10': '0.2739', 'HIT@20': '0.5282', 'NDCG@20': '0.2973'}
{'epoch': 61, 'rec_avg_loss': '0.4758', 'joint_avg_loss': '0.7511', 'cl_avg_loss': '2.7533', 'cl_pair_0_loss': '2.7533'}
{'Epoch': 61, 'HIT@5': '0.3535', 'NDCG@5': '0.2480', 'HIT@10': '0.4343', 'NDCG@10': '0.2739', 'HIT@20': '0.5269', 'NDCG@20': '0.2972'}
{'epoch': 62, 'rec_avg_loss': '0.4758', 'joint_avg_loss': '0.7515', 'cl_avg_loss': '2.7572', 'cl_pair_0_loss': '2.7572'}
{'Epoch': 62, 'HIT@5': '0.3534', 'NDCG@5': '0.2468', 'HIT@10': '0.4352', 'NDCG@10': '0.2732', 'HIT@20': '0.5278', 'NDCG@20': '0.2965'}
{'epoch': 63, 'rec_avg_loss': '0.4754', 'joint_avg_loss': '0.7500', 'cl_avg_loss': '2.7460', 'cl_pair_0_loss': '2.7460'}
{'Epoch': 63, 'HIT@5': '0.3524', 'NDCG@5': '0.2467', 'HIT@10': '0.4327', 'NDCG@10': '0.2726', 'HIT@20': '0.5296', 'NDCG@20': '0.2969'}
{'epoch': 64, 'rec_avg_loss': '0.4716', 'joint_avg_loss': '0.7458', 'cl_avg_loss': '2.7422', 'cl_pair_0_loss': '2.7422'}
{'Epoch': 64, 'HIT@5': '0.3542', 'NDCG@5': '0.2469', 'HIT@10': '0.4345', 'NDCG@10': '0.2729', 'HIT@20': '0.5289', 'NDCG@20': '0.2966'}
{'epoch': 65, 'rec_avg_loss': '0.4703', 'joint_avg_loss': '0.7428', 'cl_avg_loss': '2.7252', 'cl_pair_0_loss': '2.7252'}
{'Epoch': 65, 'HIT@5': '0.3521', 'NDCG@5': '0.2466', 'HIT@10': '0.4316', 'NDCG@10': '0.2722', 'HIT@20': '0.5296', 'NDCG@20': '0.2968'}
{'epoch': 66, 'rec_avg_loss': '0.4709', 'joint_avg_loss': '0.7420', 'cl_avg_loss': '2.7114', 'cl_pair_0_loss': '2.7114'}
{'Epoch': 66, 'HIT@5': '0.3515', 'NDCG@5': '0.2463', 'HIT@10': '0.4338', 'NDCG@10': '0.2729', 'HIT@20': '0.5301', 'NDCG@20': '0.2971'}
{'epoch': 67, 'rec_avg_loss': '0.4655', 'joint_avg_loss': '0.7358', 'cl_avg_loss': '2.7032', 'cl_pair_0_loss': '2.7032'}
{'Epoch': 67, 'HIT@5': '0.3497', 'NDCG@5': '0.2431', 'HIT@10': '0.4329', 'NDCG@10': '0.2698', 'HIT@20': '0.5313', 'NDCG@20': '0.2944'}
{'epoch': 68, 'rec_avg_loss': '0.4656', 'joint_avg_loss': '0.7378', 'cl_avg_loss': '2.7221', 'cl_pair_0_loss': '2.7221'}
{'Epoch': 68, 'HIT@5': '0.3502', 'NDCG@5': '0.2455', 'HIT@10': '0.4324', 'NDCG@10': '0.2721', 'HIT@20': '0.5328', 'NDCG@20': '0.2973'}
{'epoch': 69, 'rec_avg_loss': '0.4651', 'joint_avg_loss': '0.7383', 'cl_avg_loss': '2.7326', 'cl_pair_0_loss': '2.7326'}
{'Epoch': 69, 'HIT@5': '0.3509', 'NDCG@5': '0.2467', 'HIT@10': '0.4303', 'NDCG@10': '0.2724', 'HIT@20': '0.5312', 'NDCG@20': '0.2978'}
{'epoch': 70, 'rec_avg_loss': '0.4660', 'joint_avg_loss': '0.7366', 'cl_avg_loss': '2.7063', 'cl_pair_0_loss': '2.7063'}
{'Epoch': 70, 'HIT@5': '0.3497', 'NDCG@5': '0.2456', 'HIT@10': '0.4312', 'NDCG@10': '0.2720', 'HIT@20': '0.5325', 'NDCG@20': '0.2974'}
{'epoch': 71, 'rec_avg_loss': '0.4656', 'joint_avg_loss': '0.7374', 'cl_avg_loss': '2.7183', 'cl_pair_0_loss': '2.7183'}
{'Epoch': 71, 'HIT@5': '0.3501', 'NDCG@5': '0.2470', 'HIT@10': '0.4325', 'NDCG@10': '0.2735', 'HIT@20': '0.5318', 'NDCG@20': '0.2985'}
{'epoch': 72, 'rec_avg_loss': '0.4633', 'joint_avg_loss': '0.7321', 'cl_avg_loss': '2.6882', 'cl_pair_0_loss': '2.6882'}
{'Epoch': 72, 'HIT@5': '0.3529', 'NDCG@5': '0.2441', 'HIT@10': '0.4316', 'NDCG@10': '0.2695', 'HIT@20': '0.5345', 'NDCG@20': '0.2954'}
{'epoch': 73, 'rec_avg_loss': '0.4628', 'joint_avg_loss': '0.7327', 'cl_avg_loss': '2.6987', 'cl_pair_0_loss': '2.6987'}
{'Epoch': 73, 'HIT@5': '0.3518', 'NDCG@5': '0.2457', 'HIT@10': '0.4317', 'NDCG@10': '0.2713', 'HIT@20': '0.5319', 'NDCG@20': '0.2965'}
{'epoch': 74, 'rec_avg_loss': '0.4625', 'joint_avg_loss': '0.7322', 'cl_avg_loss': '2.6966', 'cl_pair_0_loss': '2.6966'}
{'Epoch': 74, 'HIT@5': '0.3550', 'NDCG@5': '0.2474', 'HIT@10': '0.4327', 'NDCG@10': '0.2723', 'HIT@20': '0.5330', 'NDCG@20': '0.2976'}
{'epoch': 75, 'rec_avg_loss': '0.4585', 'joint_avg_loss': '0.7277', 'cl_avg_loss': '2.6918', 'cl_pair_0_loss': '2.6918'}
{'Epoch': 75, 'HIT@5': '0.3527', 'NDCG@5': '0.2472', 'HIT@10': '0.4339', 'NDCG@10': '0.2735', 'HIT@20': '0.5342', 'NDCG@20': '0.2987'}
{'epoch': 76, 'rec_avg_loss': '0.4586', 'joint_avg_loss': '0.7271', 'cl_avg_loss': '2.6852', 'cl_pair_0_loss': '2.6852'}
{'Epoch': 76, 'HIT@5': '0.3477', 'NDCG@5': '0.2444', 'HIT@10': '0.4329', 'NDCG@10': '0.2720', 'HIT@20': '0.5348', 'NDCG@20': '0.2976'}
{'epoch': 77, 'rec_avg_loss': '0.4602', 'joint_avg_loss': '0.7278', 'cl_avg_loss': '2.6762', 'cl_pair_0_loss': '2.6762'}
{'Epoch': 77, 'HIT@5': '0.3554', 'NDCG@5': '0.2470', 'HIT@10': '0.4336', 'NDCG@10': '0.2721', 'HIT@20': '0.5362', 'NDCG@20': '0.2980'}
{'epoch': 78, 'rec_avg_loss': '0.4573', 'joint_avg_loss': '0.7224', 'cl_avg_loss': '2.6512', 'cl_pair_0_loss': '2.6512'}
{'Epoch': 78, 'HIT@5': '0.3480', 'NDCG@5': '0.2437', 'HIT@10': '0.4314', 'NDCG@10': '0.2708', 'HIT@20': '0.5358', 'NDCG@20': '0.2971'}
{'epoch': 79, 'rec_avg_loss': '0.4580', 'joint_avg_loss': '0.7260', 'cl_avg_loss': '2.6792', 'cl_pair_0_loss': '2.6792'}
{'Epoch': 79, 'HIT@5': '0.3532', 'NDCG@5': '0.2461', 'HIT@10': '0.4330', 'NDCG@10': '0.2718', 'HIT@20': '0.5372', 'NDCG@20': '0.2980'}
{'epoch': 80, 'rec_avg_loss': '0.4550', 'joint_avg_loss': '0.7230', 'cl_avg_loss': '2.6800', 'cl_pair_0_loss': '2.6800'}
{'Epoch': 80, 'HIT@5': '0.3466', 'NDCG@5': '0.2442', 'HIT@10': '0.4321', 'NDCG@10': '0.2718', 'HIT@20': '0.5355', 'NDCG@20': '0.2979'}
{'epoch': 81, 'rec_avg_loss': '0.4548', 'joint_avg_loss': '0.7197', 'cl_avg_loss': '2.6494', 'cl_pair_0_loss': '2.6494'}
{'Epoch': 81, 'HIT@5': '0.3511', 'NDCG@5': '0.2441', 'HIT@10': '0.4325', 'NDCG@10': '0.2704', 'HIT@20': '0.5372', 'NDCG@20': '0.2967'}
{'epoch': 82, 'rec_avg_loss': '0.4568', 'joint_avg_loss': '0.7212', 'cl_avg_loss': '2.6443', 'cl_pair_0_loss': '2.6443'}
{'Epoch': 82, 'HIT@5': '0.3511', 'NDCG@5': '0.2450', 'HIT@10': '0.4341', 'NDCG@10': '0.2717', 'HIT@20': '0.5362', 'NDCG@20': '0.2974'}
{'epoch': 83, 'rec_avg_loss': '0.4543', 'joint_avg_loss': '0.7195', 'cl_avg_loss': '2.6522', 'cl_pair_0_loss': '2.6522'}
{'Epoch': 83, 'HIT@5': '0.3499', 'NDCG@5': '0.2426', 'HIT@10': '0.4342', 'NDCG@10': '0.2697', 'HIT@20': '0.5377', 'NDCG@20': '0.2957'}
{'epoch': 84, 'rec_avg_loss': '0.4509', 'joint_avg_loss': '0.7155', 'cl_avg_loss': '2.6467', 'cl_pair_0_loss': '2.6467'}
{'Epoch': 84, 'HIT@5': '0.3500', 'NDCG@5': '0.2429', 'HIT@10': '0.4339', 'NDCG@10': '0.2699', 'HIT@20': '0.5351', 'NDCG@20': '0.2954'}
{'epoch': 85, 'rec_avg_loss': '0.4514', 'joint_avg_loss': '0.7171', 'cl_avg_loss': '2.6576', 'cl_pair_0_loss': '2.6576'}
{'Epoch': 85, 'HIT@5': '0.3510', 'NDCG@5': '0.2409', 'HIT@10': '0.4341', 'NDCG@10': '0.2676', 'HIT@20': '0.5371', 'NDCG@20': '0.2936'}
{'epoch': 86, 'rec_avg_loss': '0.4529', 'joint_avg_loss': '0.7164', 'cl_avg_loss': '2.6352', 'cl_pair_0_loss': '2.6352'}
{'Epoch': 86, 'HIT@5': '0.3516', 'NDCG@5': '0.2428', 'HIT@10': '0.4351', 'NDCG@10': '0.2697', 'HIT@20': '0.5384', 'NDCG@20': '0.2958'}
{'epoch': 87, 'rec_avg_loss': '0.4503', 'joint_avg_loss': '0.7138', 'cl_avg_loss': '2.6347', 'cl_pair_0_loss': '2.6347'}
{'Epoch': 87, 'HIT@5': '0.3477', 'NDCG@5': '0.2412', 'HIT@10': '0.4331', 'NDCG@10': '0.2687', 'HIT@20': '0.5371', 'NDCG@20': '0.2949'}
{'epoch': 88, 'rec_avg_loss': '0.4505', 'joint_avg_loss': '0.7145', 'cl_avg_loss': '2.6400', 'cl_pair_0_loss': '2.6400'}
{'Epoch': 88, 'HIT@5': '0.3480', 'NDCG@5': '0.2409', 'HIT@10': '0.4347', 'NDCG@10': '0.2688', 'HIT@20': '0.5360', 'NDCG@20': '0.2943'}
{'epoch': 89, 'rec_avg_loss': '0.4485', 'joint_avg_loss': '0.7100', 'cl_avg_loss': '2.6154', 'cl_pair_0_loss': '2.6154'}
{'Epoch': 89, 'HIT@5': '0.3484', 'NDCG@5': '0.2414', 'HIT@10': '0.4344', 'NDCG@10': '0.2691', 'HIT@20': '0.5383', 'NDCG@20': '0.2952'}
{'epoch': 90, 'rec_avg_loss': '0.4480', 'joint_avg_loss': '0.7120', 'cl_avg_loss': '2.6399', 'cl_pair_0_loss': '2.6399'}
{'Epoch': 90, 'HIT@5': '0.3481', 'NDCG@5': '0.2412', 'HIT@10': '0.4353', 'NDCG@10': '0.2692', 'HIT@20': '0.5378', 'NDCG@20': '0.2951'}
{'epoch': 91, 'rec_avg_loss': '0.4490', 'joint_avg_loss': '0.7125', 'cl_avg_loss': '2.6347', 'cl_pair_0_loss': '2.6347'}
{'Epoch': 91, 'HIT@5': '0.3496', 'NDCG@5': '0.2421', 'HIT@10': '0.4352', 'NDCG@10': '0.2697', 'HIT@20': '0.5393', 'NDCG@20': '0.2959'}
{'epoch': 92, 'rec_avg_loss': '0.4489', 'joint_avg_loss': '0.7078', 'cl_avg_loss': '2.5890', 'cl_pair_0_loss': '2.5890'}
{'Epoch': 92, 'HIT@5': '0.3464', 'NDCG@5': '0.2395', 'HIT@10': '0.4337', 'NDCG@10': '0.2677', 'HIT@20': '0.5366', 'NDCG@20': '0.2936'}
{'epoch': 93, 'rec_avg_loss': '0.4439', 'joint_avg_loss': '0.7061', 'cl_avg_loss': '2.6216', 'cl_pair_0_loss': '2.6216'}
{'Epoch': 93, 'HIT@5': '0.3496', 'NDCG@5': '0.2427', 'HIT@10': '0.4367', 'NDCG@10': '0.2708', 'HIT@20': '0.5412', 'NDCG@20': '0.2971'}
{'epoch': 94, 'rec_avg_loss': '0.4469', 'joint_avg_loss': '0.7074', 'cl_avg_loss': '2.6056', 'cl_pair_0_loss': '2.6056'}
{'Epoch': 94, 'HIT@5': '0.3484', 'NDCG@5': '0.2420', 'HIT@10': '0.4349', 'NDCG@10': '0.2698', 'HIT@20': '0.5402', 'NDCG@20': '0.2964'}
{'epoch': 95, 'rec_avg_loss': '0.4489', 'joint_avg_loss': '0.7081', 'cl_avg_loss': '2.5921', 'cl_pair_0_loss': '2.5921'}
{'Epoch': 95, 'HIT@5': '0.3456', 'NDCG@5': '0.2378', 'HIT@10': '0.4336', 'NDCG@10': '0.2661', 'HIT@20': '0.5382', 'NDCG@20': '0.2925'}
{'epoch': 96, 'rec_avg_loss': '0.4458', 'joint_avg_loss': '0.7054', 'cl_avg_loss': '2.5961', 'cl_pair_0_loss': '2.5961'}
{'Epoch': 96, 'HIT@5': '0.3474', 'NDCG@5': '0.2389', 'HIT@10': '0.4362', 'NDCG@10': '0.2674', 'HIT@20': '0.5398', 'NDCG@20': '0.2935'}
{'epoch': 97, 'rec_avg_loss': '0.4472', 'joint_avg_loss': '0.7073', 'cl_avg_loss': '2.6003', 'cl_pair_0_loss': '2.6003'}
{'Epoch': 97, 'HIT@5': '0.3505', 'NDCG@5': '0.2446', 'HIT@10': '0.4377', 'NDCG@10': '0.2727', 'HIT@20': '0.5417', 'NDCG@20': '0.2989'}
{'epoch': 98, 'rec_avg_loss': '0.4426', 'joint_avg_loss': '0.7034', 'cl_avg_loss': '2.6080', 'cl_pair_0_loss': '2.6080'}
{'Epoch': 98, 'HIT@5': '0.3485', 'NDCG@5': '0.2391', 'HIT@10': '0.4369', 'NDCG@10': '0.2677', 'HIT@20': '0.5398', 'NDCG@20': '0.2937'}
{'epoch': 99, 'rec_avg_loss': '0.4463', 'joint_avg_loss': '0.7051', 'cl_avg_loss': '2.5881', 'cl_pair_0_loss': '2.5881'}
{'Epoch': 99, 'HIT@5': '0.3486', 'NDCG@5': '0.2402', 'HIT@10': '0.4352', 'NDCG@10': '0.2681', 'HIT@20': '0.5400', 'NDCG@20': '0.2946'}
{'Epoch': 0, 'HIT@5': '0.3285', 'NDCG@5': '0.2760', 'HIT@10': '0.4138', 'NDCG@10': '0.3036', 'HIT@20': '0.5085', 'NDCG@20': '0.3275'}
CoSeRec-mooc-0
{'Epoch': 0, 'HIT@5': '0.3285', 'NDCG@5': '0.2760', 'HIT@10': '0.4138', 'NDCG@10': '0.3036', 'HIT@20': '0.5085', 'NDCG@20': '0.3275'}
Namespace(data_dir='../data/', output_dir='output/', data_name='mooc', do_eval=False, model_idx=0, gpu_id='0', noise_ratio=0.0, training_data_ratio=1.0, augment_threshold=4, similarity_model_name='ItemCF_IUF', augmentation_warm_up_epoches=300, base_augment_type='random', augment_type_for_short='SIM', tao=0.2, gamma=0.7, beta=0.2, substitute_rate=0.1, insert_rate=0.4, max_insert_num_per_pos=1, temperature=1.0, n_views=2, model_name='CoSeRec', hidden_size=64, num_hidden_layers=2, num_attention_heads=2, hidden_act='gelu', attention_probs_dropout_prob=0.5, hidden_dropout_prob=0.5, initializer_range=0.02, max_seq_length=50, lr=0.001, batch_size=256, epochs=100, no_cuda=False, log_freq=1, seed=1, cf_weight=0.1, rec_weight=1.0, weight_decay=0.0, adam_beta1=0.9, adam_beta2=0.999, device_type='mps', cuda_condition=False, data_file='../data/mooc.txt', item_size=1304, mask_id=1303, log_file='output/CoSeRec-mooc-0.txt')
Namespace(data_dir='../data/', output_dir='output/', data_name='mooc', do_eval=False, model_idx=0, gpu_id='0', noise_ratio=0.0, training_data_ratio=1.0, augment_threshold=4, similarity_model_name='ItemCF_IUF', augmentation_warm_up_epoches=300, base_augment_type='random', augment_type_for_short='SIM', tao=0.2, gamma=0.7, beta=0.2, substitute_rate=0.1, insert_rate=0.4, max_insert_num_per_pos=1, temperature=1.0, n_views=2, model_name='CoSeRec', hidden_size=64, num_hidden_layers=2, num_attention_heads=2, hidden_act='gelu', attention_probs_dropout_prob=0.5, hidden_dropout_prob=0.5, initializer_range=0.02, max_seq_length=50, lr=0.001, batch_size=256, epochs=100, no_cuda=False, log_freq=1, seed=1, cf_weight=0.1, rec_weight=1.0, weight_decay=0.0, adam_beta1=0.9, adam_beta2=0.999, device_type='mps', cuda_condition=False, data_file='../data/mooc.txt', item_size=1304, mask_id=1303, log_file='output/CoSeRec-mooc-0.txt')
Namespace(data_dir='../data/', output_dir='output/', data_name='mooc', do_eval=False, model_idx=0, gpu_id='0', noise_ratio=0.0, training_data_ratio=1.0, augment_threshold=4, similarity_model_name='ItemCF_IUF', augmentation_warm_up_epoches=300, base_augment_type='random', augment_type_for_short='SIM', tao=0.2, gamma=0.7, beta=0.2, substitute_rate=0.1, insert_rate=0.4, max_insert_num_per_pos=1, temperature=1.0, n_views=2, model_name='CoSeRec', hidden_size=64, num_hidden_layers=2, num_attention_heads=2, hidden_act='gelu', attention_probs_dropout_prob=0.5, hidden_dropout_prob=0.5, initializer_range=0.02, max_seq_length=50, lr=0.001, batch_size=256, epochs=2, no_cuda=False, log_freq=1, seed=1, cf_weight=0.1, rec_weight=1.0, weight_decay=0.0, adam_beta1=0.9, adam_beta2=0.999, device_type='mps', cuda_condition=False, data_file='../data/mooc.txt', item_size=1304, mask_id=1303, log_file='output/CoSeRec-mooc-0.txt')
Namespace(data_dir='../data/', output_dir='output/', data_name='mooc', do_eval=False, model_idx=0, gpu_id='0', noise_ratio=0.0, training_data_ratio=1.0, augment_threshold=4, similarity_model_name='ItemCF_IUF', augmentation_warm_up_epoches=300, base_augment_type='random', augment_type_for_short='SIM', tao=0.2, gamma=0.7, beta=0.2, substitute_rate=0.1, insert_rate=0.4, max_insert_num_per_pos=1, temperature=1.0, n_views=2, model_name='CoSeRec', hidden_size=64, num_hidden_layers=2, num_attention_heads=2, hidden_act='gelu', attention_probs_dropout_prob=0.5, hidden_dropout_prob=0.5, initializer_range=0.02, max_seq_length=50, lr=0.001, batch_size=256, epochs=2, no_cuda=False, log_freq=1, seed=1, cf_weight=0.1, rec_weight=1.0, weight_decay=0.0, adam_beta1=0.9, adam_beta2=0.999, device_type='mps', cuda_condition=False, data_file='../data/mooc.txt', item_size=1304, mask_id=1303, log_file='output/CoSeRec-mooc-0.txt')
Namespace(data_dir='../data/', output_dir='output/', data_name='mooc', do_eval=False, model_idx=0, gpu_id='0', noise_ratio=0.0, training_data_ratio=1.0, augment_threshold=4, similarity_model_name='ItemCF_IUF', augmentation_warm_up_epoches=300, base_augment_type='random', augment_type_for_short='SIM', tao=0.2, gamma=0.7, beta=0.2, substitute_rate=0.1, insert_rate=0.4, max_insert_num_per_pos=1, temperature=1.0, n_views=2, model_name='CoSeRec', hidden_size=64, num_hidden_layers=2, num_attention_heads=2, hidden_act='gelu', attention_probs_dropout_prob=0.5, hidden_dropout_prob=0.5, initializer_range=0.02, max_seq_length=50, lr=0.001, batch_size=256, epochs=2, no_cuda=False, log_freq=1, seed=1, cf_weight=0.1, rec_weight=1.0, weight_decay=0.0, adam_beta1=0.9, adam_beta2=0.999, device_type='mps', cuda_condition=False, data_file='../data/mooc.txt', item_size=1304, mask_id=1303, log_file='output/CoSeRec-mooc-0.txt')
Namespace(data_dir='../data/', output_dir='output/', data_name='mooc', do_eval=False, model_idx=0, gpu_id='0', noise_ratio=0.0, training_data_ratio=1.0, augment_threshold=4, similarity_model_name='ItemCF_IUF', augmentation_warm_up_epoches=300, base_augment_type='random', augment_type_for_short='SIM', tao=0.2, gamma=0.7, beta=0.2, substitute_rate=0.1, insert_rate=0.4, max_insert_num_per_pos=1, temperature=1.0, n_views=2, model_name='CoSeRec', hidden_size=64, num_hidden_layers=2, num_attention_heads=2, hidden_act='gelu', attention_probs_dropout_prob=0.5, hidden_dropout_prob=0.5, initializer_range=0.02, max_seq_length=50, lr=0.001, batch_size=256, epochs=1, no_cuda=False, log_freq=1, seed=1, cf_weight=0.1, rec_weight=1.0, weight_decay=0.0, adam_beta1=0.9, adam_beta2=0.999, device_type='mps', cuda_condition=False, data_file='../data/mooc.txt', item_size=1304, mask_id=1303, log_file='output/CoSeRec-mooc-0.txt')
Namespace(data_dir='../data/', output_dir='output/', data_name='mooc', do_eval=False, model_idx=0, gpu_id='0', noise_ratio=0.0, training_data_ratio=1.0, augment_threshold=4, similarity_model_name='ItemCF_IUF', augmentation_warm_up_epoches=300, base_augment_type='random', augment_type_for_short='SIM', tao=0.2, gamma=0.7, beta=0.2, substitute_rate=0.1, insert_rate=0.4, max_insert_num_per_pos=1, temperature=1.0, n_views=2, model_name='CoSeRec', hidden_size=64, num_hidden_layers=2, num_attention_heads=2, hidden_act='gelu', attention_probs_dropout_prob=0.5, hidden_dropout_prob=0.5, initializer_range=0.02, max_seq_length=50, lr=0.001, batch_size=256, epochs=2, no_cuda=False, log_freq=1, seed=1, cf_weight=0.1, rec_weight=1.0, weight_decay=0.0, adam_beta1=0.9, adam_beta2=0.999, device_type='mps', cuda_condition=False, data_file='../data/mooc.txt', item_size=1304, mask_id=1303, log_file='output/CoSeRec-mooc-0.txt')
Namespace(data_dir='../data/', output_dir='output/', data_name='mooc', do_eval=False, model_idx=0, gpu_id='0', noise_ratio=0.0, training_data_ratio=1.0, augment_threshold=4, similarity_model_name='ItemCF_IUF', augmentation_warm_up_epoches=300, base_augment_type='random', augment_type_for_short='SIM', tao=0.2, gamma=0.7, beta=0.2, substitute_rate=0.1, insert_rate=0.4, max_insert_num_per_pos=1, temperature=1.0, n_views=2, model_name='CoSeRec', hidden_size=64, num_hidden_layers=2, num_attention_heads=2, hidden_act='gelu', attention_probs_dropout_prob=0.5, hidden_dropout_prob=0.5, initializer_range=0.02, max_seq_length=50, lr=0.001, batch_size=256, epochs=2, no_cuda=False, log_freq=1, seed=1, cf_weight=0.1, rec_weight=1.0, weight_decay=0.0, adam_beta1=0.9, adam_beta2=0.999, device_type='mps', cuda_condition=False, data_file='../data/mooc.txt', item_size=1304, mask_id=1303, log_file='output/CoSeRec-mooc-0.txt')
Namespace(data_dir='../data/', output_dir='output/', data_name='mooc', do_eval=False, model_idx=0, gpu_id='0', noise_ratio=0.0, training_data_ratio=1.0, augment_threshold=4, similarity_model_name='ItemCF_IUF', augmentation_warm_up_epoches=300, base_augment_type='random', augment_type_for_short='SIM', tao=0.2, gamma=0.7, beta=0.2, substitute_rate=0.1, insert_rate=0.4, max_insert_num_per_pos=1, temperature=1.0, n_views=2, model_name='CoSeRec', hidden_size=64, num_hidden_layers=2, num_attention_heads=2, hidden_act='gelu', attention_probs_dropout_prob=0.5, hidden_dropout_prob=0.5, initializer_range=0.02, max_seq_length=50, lr=0.001, batch_size=256, epochs=2, no_cuda=False, log_freq=1, seed=1, cf_weight=0.1, rec_weight=1.0, weight_decay=0.0, adam_beta1=0.9, adam_beta2=0.999, device_type='mps', cuda_condition=False, data_file='../data/mooc.txt', item_size=1304, mask_id=1303, log_file='output/CoSeRec-mooc-0.txt')
{'Epoch': 0, 'HIT@5': '0.2097', 'NDCG@5': '0.1385', 'HIT@10': '0.3059', 'NDCG@10': '0.1701', 'HIT@20': '0.3692', 'NDCG@20': '0.1860'}
{'Epoch': 1, 'HIT@5': '0.2589', 'NDCG@5': '0.1795', 'HIT@10': '0.3654', 'NDCG@10': '0.2153', 'HIT@20': '0.4525', 'NDCG@20': '0.2371'}
{'Epoch': 0, 'HIT@5': '0.2971', 'NDCG@5': '0.2158', 'HIT@10': '0.3532', 'NDCG@10': '0.2338', 'HIT@20': '0.4313', 'NDCG@20': '0.2535'}
CoSeRec-mooc-0
{'Epoch': 0, 'HIT@5': '0.2971', 'NDCG@5': '0.2158', 'HIT@10': '0.3532', 'NDCG@10': '0.2338', 'HIT@20': '0.4313', 'NDCG@20': '0.2535'}
Namespace(data_dir='../data/', output_dir='output/', data_name='mooc', do_eval=False, model_idx=0, gpu_id='0', noise_ratio=0.0, training_data_ratio=1.0, augment_threshold=4, similarity_model_name='ItemCF_IUF', augmentation_warm_up_epoches=300, base_augment_type='random', augment_type_for_short='SIM', tao=0.2, gamma=0.7, beta=0.2, substitute_rate=0.1, insert_rate=0.4, max_insert_num_per_pos=1, temperature=1.0, n_views=2, model_name='CoSeRec', hidden_size=64, num_hidden_layers=2, num_attention_heads=2, hidden_act='gelu', attention_probs_dropout_prob=0.5, hidden_dropout_prob=0.5, initializer_range=0.02, max_seq_length=50, lr=0.001, batch_size=256, epochs=100, no_cuda=False, log_freq=1, seed=1, cf_weight=0.1, rec_weight=1.0, weight_decay=0.0, adam_beta1=0.9, adam_beta2=0.999, device_type='mps', cuda_condition=False, data_file='../data/mooc.txt', item_size=1304, mask_id=1303, log_file='output/CoSeRec-mooc-0.txt')
{'Epoch': 0, 'HIT@5': '0.2097', 'NDCG@5': '0.1385', 'HIT@10': '0.3059', 'NDCG@10': '0.1701', 'HIT@20': '0.3692', 'NDCG@20': '0.1860'}
{'Epoch': 1, 'HIT@5': '0.2589', 'NDCG@5': '0.1795', 'HIT@10': '0.3654', 'NDCG@10': '0.2153', 'HIT@20': '0.4525', 'NDCG@20': '0.2371'}
{'Epoch': 2, 'HIT@5': '0.3095', 'NDCG@5': '0.2168', 'HIT@10': '0.3819', 'NDCG@10': '0.2404', 'HIT@20': '0.4662', 'NDCG@20': '0.2617'}
{'Epoch': 3, 'HIT@5': '0.3178', 'NDCG@5': '0.2355', 'HIT@10': '0.3945', 'NDCG@10': '0.2602', 'HIT@20': '0.4879', 'NDCG@20': '0.2837'}
{'Epoch': 4, 'HIT@5': '0.3209', 'NDCG@5': '0.2252', 'HIT@10': '0.4018', 'NDCG@10': '0.2516', 'HIT@20': '0.4990', 'NDCG@20': '0.2761'}
{'Epoch': 5, 'HIT@5': '0.3288', 'NDCG@5': '0.2202', 'HIT@10': '0.4082', 'NDCG@10': '0.2459', 'HIT@20': '0.5044', 'NDCG@20': '0.2702'}
{'Epoch': 6, 'HIT@5': '0.3305', 'NDCG@5': '0.2619', 'HIT@10': '0.4124', 'NDCG@10': '0.2886', 'HIT@20': '0.5142', 'NDCG@20': '0.3142'}
{'Epoch': 7, 'HIT@5': '0.3294', 'NDCG@5': '0.2300', 'HIT@10': '0.4114', 'NDCG@10': '0.2567', 'HIT@20': '0.5193', 'NDCG@20': '0.2839'}
{'Epoch': 8, 'HIT@5': '0.3336', 'NDCG@5': '0.2335', 'HIT@10': '0.4226', 'NDCG@10': '0.2624', 'HIT@20': '0.5264', 'NDCG@20': '0.2887'}
{'Epoch': 9, 'HIT@5': '0.3362', 'NDCG@5': '0.2342', 'HIT@10': '0.4258', 'NDCG@10': '0.2633', 'HIT@20': '0.5286', 'NDCG@20': '0.2893'}
{'Epoch': 10, 'HIT@5': '0.3428', 'NDCG@5': '0.2688', 'HIT@10': '0.4260', 'NDCG@10': '0.2957', 'HIT@20': '0.5335', 'NDCG@20': '0.3228'}
{'Epoch': 11, 'HIT@5': '0.3394', 'NDCG@5': '0.2546', 'HIT@10': '0.4264', 'NDCG@10': '0.2827', 'HIT@20': '0.5366', 'NDCG@20': '0.3105'}
{'Epoch': 12, 'HIT@5': '0.3434', 'NDCG@5': '0.2367', 'HIT@10': '0.4299', 'NDCG@10': '0.2647', 'HIT@20': '0.5396', 'NDCG@20': '0.2923'}
{'Epoch': 13, 'HIT@5': '0.3398', 'NDCG@5': '0.2360', 'HIT@10': '0.4314', 'NDCG@10': '0.2658', 'HIT@20': '0.5400', 'NDCG@20': '0.2932'}
{'Epoch': 14, 'HIT@5': '0.3424', 'NDCG@5': '0.2360', 'HIT@10': '0.4331', 'NDCG@10': '0.2654', 'HIT@20': '0.5431', 'NDCG@20': '0.2933'}
{'Epoch': 15, 'HIT@5': '0.3423', 'NDCG@5': '0.2411', 'HIT@10': '0.4375', 'NDCG@10': '0.2721', 'HIT@20': '0.5489', 'NDCG@20': '0.3003'}
{'Epoch': 16, 'HIT@5': '0.3466', 'NDCG@5': '0.2402', 'HIT@10': '0.4385', 'NDCG@10': '0.2698', 'HIT@20': '0.5483', 'NDCG@20': '0.2977'}
{'Epoch': 17, 'HIT@5': '0.3441', 'NDCG@5': '0.2398', 'HIT@10': '0.4381', 'NDCG@10': '0.2704', 'HIT@20': '0.5485', 'NDCG@20': '0.2983'}
{'Epoch': 18, 'HIT@5': '0.3502', 'NDCG@5': '0.2422', 'HIT@10': '0.4421', 'NDCG@10': '0.2720', 'HIT@20': '0.5516', 'NDCG@20': '0.2997'}
{'Epoch': 19, 'HIT@5': '0.3538', 'NDCG@5': '0.2463', 'HIT@10': '0.4438', 'NDCG@10': '0.2755', 'HIT@20': '0.5539', 'NDCG@20': '0.3033'}
{'Epoch': 20, 'HIT@5': '0.3498', 'NDCG@5': '0.2419', 'HIT@10': '0.4405', 'NDCG@10': '0.2713', 'HIT@20': '0.5524', 'NDCG@20': '0.2996'}
{'Epoch': 21, 'HIT@5': '0.3538', 'NDCG@5': '0.2492', 'HIT@10': '0.4464', 'NDCG@10': '0.2792', 'HIT@20': '0.5562', 'NDCG@20': '0.3070'}
{'Epoch': 22, 'HIT@5': '0.3556', 'NDCG@5': '0.2507', 'HIT@10': '0.4468', 'NDCG@10': '0.2801', 'HIT@20': '0.5577', 'NDCG@20': '0.3082'}
{'Epoch': 23, 'HIT@5': '0.3496', 'NDCG@5': '0.2470', 'HIT@10': '0.4476', 'NDCG@10': '0.2788', 'HIT@20': '0.5569', 'NDCG@20': '0.3064'}
{'Epoch': 24, 'HIT@5': '0.3601', 'NDCG@5': '0.2631', 'HIT@10': '0.4490', 'NDCG@10': '0.2919', 'HIT@20': '0.5590', 'NDCG@20': '0.3197'}
{'Epoch': 25, 'HIT@5': '0.3553', 'NDCG@5': '0.2454', 'HIT@10': '0.4472', 'NDCG@10': '0.2753', 'HIT@20': '0.5593', 'NDCG@20': '0.3036'}
{'Epoch': 26, 'HIT@5': '0.3571', 'NDCG@5': '0.2818', 'HIT@10': '0.4488', 'NDCG@10': '0.3115', 'HIT@20': '0.5586', 'NDCG@20': '0.3393'}
{'Epoch': 27, 'HIT@5': '0.3636', 'NDCG@5': '0.2537', 'HIT@10': '0.4502', 'NDCG@10': '0.2815', 'HIT@20': '0.5612', 'NDCG@20': '0.3096'}
{'Epoch': 28, 'HIT@5': '0.3590', 'NDCG@5': '0.2525', 'HIT@10': '0.4494', 'NDCG@10': '0.2818', 'HIT@20': '0.5591', 'NDCG@20': '0.3095'}
{'Epoch': 29, 'HIT@5': '0.3637', 'NDCG@5': '0.2567', 'HIT@10': '0.4538', 'NDCG@10': '0.2859', 'HIT@20': '0.5607', 'NDCG@20': '0.3130'}
{'Epoch': 30, 'HIT@5': '0.3604', 'NDCG@5': '0.2519', 'HIT@10': '0.4525', 'NDCG@10': '0.2817', 'HIT@20': '0.5633', 'NDCG@20': '0.3098'}
{'Epoch': 31, 'HIT@5': '0.3669', 'NDCG@5': '0.2568', 'HIT@10': '0.4538', 'NDCG@10': '0.2848', 'HIT@20': '0.5656', 'NDCG@20': '0.3131'}
{'Epoch': 32, 'HIT@5': '0.3627', 'NDCG@5': '0.2549', 'HIT@10': '0.4554', 'NDCG@10': '0.2849', 'HIT@20': '0.5655', 'NDCG@20': '0.3127'}
{'Epoch': 33, 'HIT@5': '0.3572', 'NDCG@5': '0.2526', 'HIT@10': '0.4552', 'NDCG@10': '0.2844', 'HIT@20': '0.5640', 'NDCG@20': '0.3120'}
{'Epoch': 34, 'HIT@5': '0.3662', 'NDCG@5': '0.2573', 'HIT@10': '0.4572', 'NDCG@10': '0.2867', 'HIT@20': '0.5687', 'NDCG@20': '0.3149'}
{'Epoch': 35, 'HIT@5': '0.3657', 'NDCG@5': '0.2562', 'HIT@10': '0.4601', 'NDCG@10': '0.2866', 'HIT@20': '0.5644', 'NDCG@20': '0.3130'}
{'Epoch': 36, 'HIT@5': '0.3651', 'NDCG@5': '0.2579', 'HIT@10': '0.4539', 'NDCG@10': '0.2866', 'HIT@20': '0.5660', 'NDCG@20': '0.3150'}
{'Epoch': 37, 'HIT@5': '0.3658', 'NDCG@5': '0.2578', 'HIT@10': '0.4581', 'NDCG@10': '0.2877', 'HIT@20': '0.5670', 'NDCG@20': '0.3152'}
{'Epoch': 38, 'HIT@5': '0.3688', 'NDCG@5': '0.2583', 'HIT@10': '0.4540', 'NDCG@10': '0.2858', 'HIT@20': '0.5644', 'NDCG@20': '0.3137'}
{'Epoch': 39, 'HIT@5': '0.3697', 'NDCG@5': '0.2602', 'HIT@10': '0.4568', 'NDCG@10': '0.2882', 'HIT@20': '0.5664', 'NDCG@20': '0.3160'}
{'Epoch': 40, 'HIT@5': '0.3717', 'NDCG@5': '0.2612', 'HIT@10': '0.4648', 'NDCG@10': '0.2911', 'HIT@20': '0.5687', 'NDCG@20': '0.3173'}
{'Epoch': 41, 'HIT@5': '0.3689', 'NDCG@5': '0.2604', 'HIT@10': '0.4587', 'NDCG@10': '0.2893', 'HIT@20': '0.5656', 'NDCG@20': '0.3164'}
{'Epoch': 42, 'HIT@5': '0.3740', 'NDCG@5': '0.2630', 'HIT@10': '0.4617', 'NDCG@10': '0.2913', 'HIT@20': '0.5681', 'NDCG@20': '0.3182'}
{'Epoch': 43, 'HIT@5': '0.3773', 'NDCG@5': '0.2672', 'HIT@10': '0.4629', 'NDCG@10': '0.2948', 'HIT@20': '0.5675', 'NDCG@20': '0.3211'}
{'Epoch': 44, 'HIT@5': '0.3685', 'NDCG@5': '0.2732', 'HIT@10': '0.4658', 'NDCG@10': '0.3045', 'HIT@20': '0.5677', 'NDCG@20': '0.3302'}
{'Epoch': 45, 'HIT@5': '0.3718', 'NDCG@5': '0.2632', 'HIT@10': '0.4641', 'NDCG@10': '0.2929', 'HIT@20': '0.5698', 'NDCG@20': '0.3195'}
{'Epoch': 46, 'HIT@5': '0.3705', 'NDCG@5': '0.2755', 'HIT@10': '0.4625', 'NDCG@10': '0.3052', 'HIT@20': '0.5707', 'NDCG@20': '0.3326'}
{'Epoch': 47, 'HIT@5': '0.3684', 'NDCG@5': '0.2593', 'HIT@10': '0.4659', 'NDCG@10': '0.2908', 'HIT@20': '0.5700', 'NDCG@20': '0.3171'}
{'Epoch': 48, 'HIT@5': '0.3723', 'NDCG@5': '0.2939', 'HIT@10': '0.4625', 'NDCG@10': '0.3229', 'HIT@20': '0.5691', 'NDCG@20': '0.3497'}
{'Epoch': 49, 'HIT@5': '0.3748', 'NDCG@5': '0.2974', 'HIT@10': '0.4669', 'NDCG@10': '0.3271', 'HIT@20': '0.5693', 'NDCG@20': '0.3529'}
{'Epoch': 50, 'HIT@5': '0.3700', 'NDCG@5': '0.2922', 'HIT@10': '0.4666', 'NDCG@10': '0.3233', 'HIT@20': '0.5703', 'NDCG@20': '0.3494'}
{'Epoch': 51, 'HIT@5': '0.3700', 'NDCG@5': '0.2761', 'HIT@10': '0.4657', 'NDCG@10': '0.3069', 'HIT@20': '0.5700', 'NDCG@20': '0.3331'}
{'Epoch': 52, 'HIT@5': '0.3763', 'NDCG@5': '0.2796', 'HIT@10': '0.4673', 'NDCG@10': '0.3090', 'HIT@20': '0.5714', 'NDCG@20': '0.3352'}
{'Epoch': 53, 'HIT@5': '0.3744', 'NDCG@5': '0.2773', 'HIT@10': '0.4665', 'NDCG@10': '0.3070', 'HIT@20': '0.5726', 'NDCG@20': '0.3338'}
{'Epoch': 54, 'HIT@5': '0.3748', 'NDCG@5': '0.2658', 'HIT@10': '0.4668', 'NDCG@10': '0.2956', 'HIT@20': '0.5699', 'NDCG@20': '0.3216'}
{'Epoch': 55, 'HIT@5': '0.3791', 'NDCG@5': '0.2959', 'HIT@10': '0.4685', 'NDCG@10': '0.3248', 'HIT@20': '0.5730', 'NDCG@20': '0.3510'}
{'Epoch': 56, 'HIT@5': '0.3736', 'NDCG@5': '0.2646', 'HIT@10': '0.4670', 'NDCG@10': '0.2948', 'HIT@20': '0.5721', 'NDCG@20': '0.3213'}
{'Epoch': 57, 'HIT@5': '0.3749', 'NDCG@5': '0.2637', 'HIT@10': '0.4688', 'NDCG@10': '0.2939', 'HIT@20': '0.5739', 'NDCG@20': '0.3203'}
{'Epoch': 58, 'HIT@5': '0.3764', 'NDCG@5': '0.2643', 'HIT@10': '0.4699', 'NDCG@10': '0.2946', 'HIT@20': '0.5732', 'NDCG@20': '0.3206'}
{'Epoch': 59, 'HIT@5': '0.3753', 'NDCG@5': '0.2786', 'HIT@10': '0.4681', 'NDCG@10': '0.3087', 'HIT@20': '0.5734', 'NDCG@20': '0.3353'}
{'Epoch': 60, 'HIT@5': '0.3784', 'NDCG@5': '0.2989', 'HIT@10': '0.4665', 'NDCG@10': '0.3273', 'HIT@20': '0.5716', 'NDCG@20': '0.3539'}
{'Epoch': 61, 'HIT@5': '0.3793', 'NDCG@5': '0.2987', 'HIT@10': '0.4654', 'NDCG@10': '0.3265', 'HIT@20': '0.5728', 'NDCG@20': '0.3536'}
{'Epoch': 62, 'HIT@5': '0.3759', 'NDCG@5': '0.2951', 'HIT@10': '0.4665', 'NDCG@10': '0.3244', 'HIT@20': '0.5705', 'NDCG@20': '0.3507'}
{'Epoch': 63, 'HIT@5': '0.3820', 'NDCG@5': '0.2683', 'HIT@10': '0.4681', 'NDCG@10': '0.2960', 'HIT@20': '0.5753', 'NDCG@20': '0.3230'}
{'Epoch': 64, 'HIT@5': '0.3784', 'NDCG@5': '0.2650', 'HIT@10': '0.4675', 'NDCG@10': '0.2937', 'HIT@20': '0.5715', 'NDCG@20': '0.3199'}
{'Epoch': 65, 'HIT@5': '0.3750', 'NDCG@5': '0.2959', 'HIT@10': '0.4692', 'NDCG@10': '0.3264', 'HIT@20': '0.5727', 'NDCG@20': '0.3525'}
{'Epoch': 66, 'HIT@5': '0.3801', 'NDCG@5': '0.2996', 'HIT@10': '0.4674', 'NDCG@10': '0.3277', 'HIT@20': '0.5733', 'NDCG@20': '0.3544'}
{'Epoch': 67, 'HIT@5': '0.3758', 'NDCG@5': '0.2949', 'HIT@10': '0.4696', 'NDCG@10': '0.3251', 'HIT@20': '0.5732', 'NDCG@20': '0.3512'}
{'Epoch': 68, 'HIT@5': '0.3775', 'NDCG@5': '0.2833', 'HIT@10': '0.4698', 'NDCG@10': '0.3132', 'HIT@20': '0.5729', 'NDCG@20': '0.3392'}
{'Epoch': 69, 'HIT@5': '0.3813', 'NDCG@5': '0.3008', 'HIT@10': '0.4715', 'NDCG@10': '0.3299', 'HIT@20': '0.5751', 'NDCG@20': '0.3559'}
{'Epoch': 70, 'HIT@5': '0.3802', 'NDCG@5': '0.2992', 'HIT@10': '0.4688', 'NDCG@10': '0.3278', 'HIT@20': '0.5727', 'NDCG@20': '0.3540'}
{'Epoch': 71, 'HIT@5': '0.3765', 'NDCG@5': '0.2932', 'HIT@10': '0.4705', 'NDCG@10': '0.3235', 'HIT@20': '0.5739', 'NDCG@20': '0.3495'}
{'Epoch': 72, 'HIT@5': '0.3810', 'NDCG@5': '0.2986', 'HIT@10': '0.4709', 'NDCG@10': '0.3276', 'HIT@20': '0.5735', 'NDCG@20': '0.3535'}
{'Epoch': 73, 'HIT@5': '0.3802', 'NDCG@5': '0.2838', 'HIT@10': '0.4706', 'NDCG@10': '0.3129', 'HIT@20': '0.5737', 'NDCG@20': '0.3389'}
{'Epoch': 74, 'HIT@5': '0.3836', 'NDCG@5': '0.3042', 'HIT@10': '0.4693', 'NDCG@10': '0.3318', 'HIT@20': '0.5725', 'NDCG@20': '0.3578'}
{'Epoch': 75, 'HIT@5': '0.3787', 'NDCG@5': '0.2983', 'HIT@10': '0.4706', 'NDCG@10': '0.3279', 'HIT@20': '0.5739', 'NDCG@20': '0.3540'}
{'Epoch': 76, 'HIT@5': '0.3839', 'NDCG@5': '0.3046', 'HIT@10': '0.4710', 'NDCG@10': '0.3327', 'HIT@20': '0.5734', 'NDCG@20': '0.3586'}
{'Epoch': 77, 'HIT@5': '0.3831', 'NDCG@5': '0.3016', 'HIT@10': '0.4723', 'NDCG@10': '0.3304', 'HIT@20': '0.5757', 'NDCG@20': '0.3564'}
{'Epoch': 78, 'HIT@5': '0.3851', 'NDCG@5': '0.3050', 'HIT@10': '0.4728', 'NDCG@10': '0.3333', 'HIT@20': '0.5760', 'NDCG@20': '0.3593'}
{'Epoch': 79, 'HIT@5': '0.3752', 'NDCG@5': '0.2967', 'HIT@10': '0.4696', 'NDCG@10': '0.3271', 'HIT@20': '0.5704', 'NDCG@20': '0.3525'}
{'Epoch': 80, 'HIT@5': '0.3769', 'NDCG@5': '0.2987', 'HIT@10': '0.4728', 'NDCG@10': '0.3296', 'HIT@20': '0.5738', 'NDCG@20': '0.3550'}
{'Epoch': 81, 'HIT@5': '0.3777', 'NDCG@5': '0.2664', 'HIT@10': '0.4728', 'NDCG@10': '0.2972', 'HIT@20': '0.5749', 'NDCG@20': '0.3229'}
{'Epoch': 82, 'HIT@5': '0.3832', 'NDCG@5': '0.2726', 'HIT@10': '0.4728', 'NDCG@10': '0.3015', 'HIT@20': '0.5730', 'NDCG@20': '0.3267'}
{'Epoch': 83, 'HIT@5': '0.3747', 'NDCG@5': '0.2974', 'HIT@10': '0.4696', 'NDCG@10': '0.3281', 'HIT@20': '0.5755', 'NDCG@20': '0.3549'}
{'Epoch': 84, 'HIT@5': '0.3852', 'NDCG@5': '0.2848', 'HIT@10': '0.4726', 'NDCG@10': '0.3130', 'HIT@20': '0.5748', 'NDCG@20': '0.3387'}
{'Epoch': 85, 'HIT@5': '0.3862', 'NDCG@5': '0.3078', 'HIT@10': '0.4734', 'NDCG@10': '0.3360', 'HIT@20': '0.5754', 'NDCG@20': '0.3617'}
{'Epoch': 86, 'HIT@5': '0.3807', 'NDCG@5': '0.3041', 'HIT@10': '0.4725', 'NDCG@10': '0.3337', 'HIT@20': '0.5766', 'NDCG@20': '0.3599'}
{'Epoch': 87, 'HIT@5': '0.3810', 'NDCG@5': '0.2828', 'HIT@10': '0.4694', 'NDCG@10': '0.3113', 'HIT@20': '0.5728', 'NDCG@20': '0.3374'}
{'Epoch': 88, 'HIT@5': '0.3838', 'NDCG@5': '0.2823', 'HIT@10': '0.4719', 'NDCG@10': '0.3106', 'HIT@20': '0.5750', 'NDCG@20': '0.3366'}
{'Epoch': 89, 'HIT@5': '0.3804', 'NDCG@5': '0.3004', 'HIT@10': '0.4709', 'NDCG@10': '0.3296', 'HIT@20': '0.5732', 'NDCG@20': '0.3553'}
{'Epoch': 90, 'HIT@5': '0.3815', 'NDCG@5': '0.3039', 'HIT@10': '0.4733', 'NDCG@10': '0.3337', 'HIT@20': '0.5750', 'NDCG@20': '0.3593'}
{'Epoch': 91, 'HIT@5': '0.3873', 'NDCG@5': '0.3027', 'HIT@10': '0.4739', 'NDCG@10': '0.3306', 'HIT@20': '0.5764', 'NDCG@20': '0.3564'}
{'Epoch': 92, 'HIT@5': '0.3842', 'NDCG@5': '0.3052', 'HIT@10': '0.4733', 'NDCG@10': '0.3341', 'HIT@20': '0.5756', 'NDCG@20': '0.3599'}
{'Epoch': 93, 'HIT@5': '0.3860', 'NDCG@5': '0.3073', 'HIT@10': '0.4728', 'NDCG@10': '0.3353', 'HIT@20': '0.5747', 'NDCG@20': '0.3610'}
{'Epoch': 94, 'HIT@5': '0.3833', 'NDCG@5': '0.2868', 'HIT@10': '0.4735', 'NDCG@10': '0.3159', 'HIT@20': '0.5749', 'NDCG@20': '0.3415'}
{'Epoch': 95, 'HIT@5': '0.3835', 'NDCG@5': '0.3074', 'HIT@10': '0.4708', 'NDCG@10': '0.3357', 'HIT@20': '0.5749', 'NDCG@20': '0.3620'}
{'Epoch': 96, 'HIT@5': '0.3856', 'NDCG@5': '0.2888', 'HIT@10': '0.4721', 'NDCG@10': '0.3167', 'HIT@20': '0.5731', 'NDCG@20': '0.3422'}
{'Epoch': 97, 'HIT@5': '0.3830', 'NDCG@5': '0.2744', 'HIT@10': '0.4731', 'NDCG@10': '0.3035', 'HIT@20': '0.5756', 'NDCG@20': '0.3293'}
{'Epoch': 98, 'HIT@5': '0.3861', 'NDCG@5': '0.3034', 'HIT@10': '0.4711', 'NDCG@10': '0.3308', 'HIT@20': '0.5738', 'NDCG@20': '0.3567'}
{'Epoch': 99, 'HIT@5': '0.3818', 'NDCG@5': '0.2840', 'HIT@10': '0.4702', 'NDCG@10': '0.3126', 'HIT@20': '0.5742', 'NDCG@20': '0.3388'}
{'Epoch': 0, 'HIT@5': '0.3649', 'NDCG@5': '0.3081', 'HIT@10': '0.4433', 'NDCG@10': '0.3334', 'HIT@20': '0.5491', 'NDCG@20': '0.3599'}
CoSeRec-mooc-0
{'Epoch': 0, 'HIT@5': '0.3649', 'NDCG@5': '0.3081', 'HIT@10': '0.4433', 'NDCG@10': '0.3334', 'HIT@20': '0.5491', 'NDCG@20': '0.3599'}
Namespace(data_dir='../data/', output_dir='output/', data_name='mooc', do_eval=False, model_idx=0, gpu_id='0', noise_ratio=0.0, training_data_ratio=1.0, augment_threshold=4, similarity_model_name='ItemCF_IUF', augmentation_warm_up_epoches=300, base_augment_type='random', augment_type_for_short='SIM', tao=0.2, gamma=0.7, beta=0.2, substitute_rate=0.1, insert_rate=0.4, max_insert_num_per_pos=1, temperature=1.0, n_views=2, model_name='CoSeRec', hidden_size=64, num_hidden_layers=2, num_attention_heads=2, hidden_act='gelu', attention_probs_dropout_prob=0.5, hidden_dropout_prob=0.5, initializer_range=0.02, max_seq_length=50, lr=0.001, batch_size=256, epochs=5, no_cuda=False, log_freq=1, seed=1, cf_weight=0.1, rec_weight=1.0, weight_decay=0.0, adam_beta1=0.9, adam_beta2=0.999, device_type='mps', cuda_condition=False, data_file='../data/mooc.txt', item_size=1304, mask_id=1303, log_file='output/CoSeRec-mooc-0.txt')
{'Epoch': 0, 'HIT@5': '0.2097', 'NDCG@5': '0.1385', 'HIT@10': '0.3059', 'NDCG@10': '0.1701', 'HIT@20': '0.3692', 'NDCG@20': '0.1860'}
{'Epoch': 1, 'HIT@5': '0.2589', 'NDCG@5': '0.1795', 'HIT@10': '0.3654', 'NDCG@10': '0.2153', 'HIT@20': '0.4525', 'NDCG@20': '0.2371'}
{'Epoch': 2, 'HIT@5': '0.3095', 'NDCG@5': '0.2168', 'HIT@10': '0.3819', 'NDCG@10': '0.2404', 'HIT@20': '0.4662', 'NDCG@20': '0.2617'}
{'Epoch': 3, 'HIT@5': '0.3178', 'NDCG@5': '0.2355', 'HIT@10': '0.3945', 'NDCG@10': '0.2602', 'HIT@20': '0.4879', 'NDCG@20': '0.2837'}
{'Epoch': 4, 'HIT@5': '0.3209', 'NDCG@5': '0.2252', 'HIT@10': '0.4018', 'NDCG@10': '0.2516', 'HIT@20': '0.4990', 'NDCG@20': '0.2761'}
{'Epoch': 0, 'HIT@5': '0.3091', 'NDCG@5': '0.2329', 'HIT@10': '0.3764', 'NDCG@10': '0.2544', 'HIT@20': '0.4576', 'NDCG@20': '0.2749'}
CoSeRec-mooc-0
{'Epoch': 0, 'HIT@5': '0.3091', 'NDCG@5': '0.2329', 'HIT@10': '0.3764', 'NDCG@10': '0.2544', 'HIT@20': '0.4576', 'NDCG@20': '0.2749'}
Namespace(data_dir='../data/', output_dir='output/', data_name='mooc', do_eval=False, model_idx=0, gpu_id='0', noise_ratio=0.0, training_data_ratio=1.0, augment_threshold=4, similarity_model_name='ItemCF_IUF', augmentation_warm_up_epoches=300, base_augment_type='random', augment_type_for_short='SIM', tao=0.2, gamma=0.7, beta=0.2, substitute_rate=0.1, insert_rate=0.4, max_insert_num_per_pos=1, temperature=1.0, n_views=2, model_name='CoSeRec', hidden_size=64, num_hidden_layers=2, num_attention_heads=2, hidden_act='gelu', attention_probs_dropout_prob=0.5, hidden_dropout_prob=0.5, initializer_range=0.02, max_seq_length=50, lr=0.001, batch_size=256, epochs=10, no_cuda=False, log_freq=1, seed=1, cf_weight=0.1, rec_weight=1.0, weight_decay=0.0, adam_beta1=0.9, adam_beta2=0.999, device_type='mps', cuda_condition=False, data_file='../data/mooc.txt', item_size=1304, mask_id=1303, log_file='output/CoSeRec-mooc-0.txt')
{'Epoch': 0, 'HIT@5': '0.2097', 'NDCG@5': '0.1385', 'HIT@10': '0.3059', 'NDCG@10': '0.1701', 'HIT@20': '0.3692', 'NDCG@20': '0.1860'}
{'Epoch': 1, 'HIT@5': '0.2589', 'NDCG@5': '0.1795', 'HIT@10': '0.3654', 'NDCG@10': '0.2153', 'HIT@20': '0.4525', 'NDCG@20': '0.2371'}
{'Epoch': 2, 'HIT@5': '0.3095', 'NDCG@5': '0.2168', 'HIT@10': '0.3819', 'NDCG@10': '0.2404', 'HIT@20': '0.4662', 'NDCG@20': '0.2617'}
{'Epoch': 3, 'HIT@5': '0.3178', 'NDCG@5': '0.2355', 'HIT@10': '0.3945', 'NDCG@10': '0.2602', 'HIT@20': '0.4879', 'NDCG@20': '0.2837'}
{'Epoch': 4, 'HIT@5': '0.3209', 'NDCG@5': '0.2252', 'HIT@10': '0.4018', 'NDCG@10': '0.2516', 'HIT@20': '0.4990', 'NDCG@20': '0.2761'}
{'Epoch': 5, 'HIT@5': '0.3288', 'NDCG@5': '0.2202', 'HIT@10': '0.4082', 'NDCG@10': '0.2459', 'HIT@20': '0.5044', 'NDCG@20': '0.2702'}
{'Epoch': 6, 'HIT@5': '0.3305', 'NDCG@5': '0.2619', 'HIT@10': '0.4124', 'NDCG@10': '0.2886', 'HIT@20': '0.5142', 'NDCG@20': '0.3142'}
{'Epoch': 7, 'HIT@5': '0.3294', 'NDCG@5': '0.2300', 'HIT@10': '0.4114', 'NDCG@10': '0.2567', 'HIT@20': '0.5193', 'NDCG@20': '0.2839'}
{'Epoch': 8, 'HIT@5': '0.3336', 'NDCG@5': '0.2335', 'HIT@10': '0.4226', 'NDCG@10': '0.2624', 'HIT@20': '0.5264', 'NDCG@20': '0.2887'}
{'Epoch': 9, 'HIT@5': '0.3362', 'NDCG@5': '0.2342', 'HIT@10': '0.4258', 'NDCG@10': '0.2633', 'HIT@20': '0.5286', 'NDCG@20': '0.2893'}
{'Epoch': 0, 'HIT@5': '0.3265', 'NDCG@5': '0.2690', 'HIT@10': '0.3943', 'NDCG@10': '0.2908', 'HIT@20': '0.4842', 'NDCG@20': '0.3134'}
CoSeRec-mooc-0
{'Epoch': 0, 'HIT@5': '0.3265', 'NDCG@5': '0.2690', 'HIT@10': '0.3943', 'NDCG@10': '0.2908', 'HIT@20': '0.4842', 'NDCG@20': '0.3134'}
