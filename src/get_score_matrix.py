#!/usr/bin/env python3
"""
获取计算HIT和NDCG之前的原始评分矩阵
这是模型对所有物品打分后的矩阵，用于理解指标计算的基础
"""

import torch
import numpy as np

def get_raw_score_matrix(trainer, dataloader, dataset_name="Unknown"):
    """获取原始评分矩阵"""
    print(f"\n=== 获取{dataset_name}的原始评分矩阵 ===")
    
    trainer.model.eval()
    all_score_matrices = []  # 存储每个用户对所有物品的评分
    all_target_items = []    # 存储每个用户的目标物品
    all_user_ids = []        # 存储用户ID
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(dataloader):
            batch = tuple(t.to(trainer.device) for t in batch)
            user_ids, input_ids, target_pos, target_neg, answers = batch
            
            # 获取序列表示
            sequence_output = trainer.model.transformer_encoder(input_ids)
            sequence_output = sequence_output[:, -1, :]  # 取最后一个位置的表示
            
            # 计算对所有物品的评分 - 这就是我们要的原始评分矩阵！
            test_item_emb = trainer.model.item_embeddings.weight  # 所有物品的嵌入
            rating_pred = torch.matmul(sequence_output, test_item_emb.transpose(0, 1))
            # rating_pred shape: [batch_size, num_items] - 每行是一个用户对所有物品的评分
            
            # 转换为numpy并保存
            rating_pred_np = rating_pred.cpu().numpy()
            user_ids_np = user_ids.cpu().numpy()
            answers_np = answers.cpu().numpy()
            
            all_score_matrices.append(rating_pred_np)
            all_user_ids.extend(user_ids_np)
            all_target_items.extend(answers_np)
            
            if batch_idx == 0:
                print(f"评分矩阵形状: {rating_pred_np.shape}")
                print(f"每个用户对 {rating_pred_np.shape[1]} 个物品进行评分")
                print(f"第一个用户的评分范围: [{rating_pred_np[0].min():.4f}, {rating_pred_np[0].max():.4f}]")
    
    # 合并所有批次的评分矩阵
    full_score_matrix = np.vstack(all_score_matrices)
    all_target_items = np.array(all_target_items)
    all_user_ids = np.array(all_user_ids)
    
    print(f"完整评分矩阵形状: {full_score_matrix.shape}")
    print(f"总用户数: {len(all_user_ids)}")
    print(f"物品总数: {full_score_matrix.shape[1]}")
    
    # 分析评分分布
    print(f"\n=== {dataset_name}评分矩阵统计 ===")
    print(f"评分均值: {full_score_matrix.mean():.4f}")
    print(f"评分标准差: {full_score_matrix.std():.4f}")
    print(f"评分最大值: {full_score_matrix.max():.4f}")
    print(f"评分最小值: {full_score_matrix.min():.4f}")
    
    # 显示前几个用户的评分示例
    print(f"\n=== 前3个用户的评分示例 ===")
    for i in range(min(3, full_score_matrix.shape[0])):
        user_scores = full_score_matrix[i]
        target_item = all_target_items[i][0] if all_target_items[i][0] != 0 else None
        
        # 找到评分最高的前5个物品
        top5_indices = np.argsort(user_scores)[-5:][::-1]
        top5_scores = user_scores[top5_indices]
        
        print(f"用户 {i+1} (ID: {all_user_ids[i]}):")
        print(f"  目标物品: {target_item}, 目标物品评分: {user_scores[target_item] if target_item else 'N/A'}")
        print(f"  Top 5 物品: {list(zip(top5_indices, top5_scores))}")
        
        if target_item is not None:
            # 目标物品的排名
            target_rank = np.sum(user_scores > user_scores[target_item]) + 1
            print(f"  目标物品排名: {target_rank}")
    
    return {
        'score_matrix': full_score_matrix,
        'user_ids': all_user_ids,
        'target_items': all_target_items,
        'dataset_name': dataset_name
    }

def analyze_score_differences(valid_data, test_data):
    """分析验证集和测试集评分矩阵的差异"""
    print(f"\n" + "="*60)
    print(f"验证集 vs 测试集 评分矩阵对比")
    print(f"="*60)
    
    valid_matrix = valid_data['score_matrix']
    test_matrix = test_data['score_matrix']
    
    print(f"验证集评分矩阵: {valid_matrix.shape}")
    print(f"测试集评分矩阵: {test_matrix.shape}")
    
    print(f"\n=== 评分分布对比 ===")
    print(f"验证集 - 均值: {valid_matrix.mean():.4f}, 标准差: {valid_matrix.std():.4f}")
    print(f"测试集 - 均值: {test_matrix.mean():.4f}, 标准差: {test_matrix.std():.4f}")
    
    # 分析目标物品的评分
    valid_targets = valid_data['target_items']
    test_targets = test_data['target_items']
    
    valid_target_scores = []
    test_target_scores = []
    valid_target_ranks = []
    test_target_ranks = []
    
    # 计算目标物品的评分和排名
    for i, target in enumerate(valid_targets):
        if target[0] != 0:
            target_score = valid_matrix[i, target[0]]
            target_rank = np.sum(valid_matrix[i] > target_score) + 1
            valid_target_scores.append(target_score)
            valid_target_ranks.append(target_rank)
    
    for i, target in enumerate(test_targets):
        if target[0] != 0:
            target_score = test_matrix[i, target[0]]
            target_rank = np.sum(test_matrix[i] > target_score) + 1
            test_target_scores.append(target_score)
            test_target_ranks.append(target_rank)
    
    print(f"\n=== 目标物品分析 ===")
    print(f"验证集目标物品平均评分: {np.mean(valid_target_scores):.4f}")
    print(f"测试集目标物品平均评分: {np.mean(test_target_scores):.4f}")
    print(f"验证集目标物品平均排名: {np.mean(valid_target_ranks):.2f}")
    print(f"测试集目标物品平均排名: {np.mean(test_target_ranks):.2f}")
    
    # 分析Top-K命中情况
    valid_top5 = np.mean([1 if rank <= 5 else 0 for rank in valid_target_ranks])
    test_top5 = np.mean([1 if rank <= 5 else 0 for rank in test_target_ranks])
    valid_top10 = np.mean([1 if rank <= 10 else 0 for rank in valid_target_ranks])
    test_top10 = np.mean([1 if rank <= 10 else 0 for rank in test_target_ranks])
    
    print(f"\n=== 从原始评分矩阵计算的指标 ===")
    print(f"验证集 HIT@5: {valid_top5:.4f}, HIT@10: {valid_top10:.4f}")
    print(f"测试集 HIT@5: {test_top5:.4f}, HIT@10: {test_top10:.4f}")
    
    # 计算NDCG
    def calculate_ndcg(ranks, k):
        ndcg_scores = []
        for rank in ranks:
            if rank <= k:
                ndcg_scores.append(1.0 / np.log2(rank + 1))
            else:
                ndcg_scores.append(0.0)
        return np.mean(ndcg_scores)
    
    valid_ndcg5 = calculate_ndcg(valid_target_ranks, 5)
    test_ndcg5 = calculate_ndcg(test_target_ranks, 5)
    valid_ndcg10 = calculate_ndcg(valid_target_ranks, 10)
    test_ndcg10 = calculate_ndcg(test_target_ranks, 10)
    
    print(f"验证集 NDCG@5: {valid_ndcg5:.4f}, NDCG@10: {valid_ndcg10:.4f}")
    print(f"测试集 NDCG@5: {test_ndcg5:.4f}, NDCG@10: {test_ndcg10:.4f}")
    
    print(f"\n=== 现象解释 ===")
    if np.mean(test_target_ranks) < np.mean(valid_target_ranks):
        print(f"✅ 测试集目标物品排名更靠前 (平均排名差: {np.mean(valid_target_ranks) - np.mean(test_target_ranks):.2f})")
        print(f"   这解释了为什么NDCG更高")
    else:
        print(f"❌ 验证集目标物品排名更靠前")
    
    if test_top5 > valid_top5:
        print(f"✅ 测试集Top-5命中率更高 ({test_top5:.3f} vs {valid_top5:.3f})")
    else:
        print(f"❌ 验证集Top-5命中率更高 ({valid_top5:.3f} vs {test_top5:.3f})")

def save_score_matrix(data, filename):
    """保存评分矩阵到文件"""
    print(f"\n正在保存{data['dataset_name']}评分矩阵到: {filename}")
    
    # 保存为numpy格式
    np.savez(filename, 
             score_matrix=data['score_matrix'],
             user_ids=data['user_ids'],
             target_items=data['target_items'])
    
    # 同时保存为CSV格式（前100个用户，便于查看）
    csv_filename = filename.replace('.npz', '_sample.csv')
    sample_matrix = data['score_matrix'][:100]  # 前100个用户
    np.savetxt(csv_filename, sample_matrix, delimiter=',', fmt='%.6f')
    
    print(f"评分矩阵已保存到: {filename}")
    print(f"样本CSV已保存到: {csv_filename}")

if __name__ == "__main__":
    print("这是评分矩阵分析工具，请在main.py中集成使用")
