# CoSeRec算法完整流程说明文档

## 项目概述
CoSeRec (Contrastive Self-supervised Sequential Recommendation with Robust Augmentation) 是一个基于对比学习的序列推荐算法，通过多种数据增强技术和自监督学习提升推荐性能。

## 目录结构
```
CoSeRec/
├── data/                           # 数据集目录
│   ├── Beauty.txt                  # Beauty数据集
│   ├── Sports_and_Outdoors.txt     # Sports数据集
│   ├── Toys_and_Games.txt          # Toys数据集
│   └── Yelp.txt                    # Yelp数据集
├── src/                            # 源代码目录
│   ├── main.py                     # 主程序入口
│   ├── models.py                   # 模型定义
│   ├── trainers.py                 # 训练器
│   ├── datasets.py                 # 数据集处理
│   ├── data_augmentation.py        # 数据增强模块
│   ├── modules.py                  # 核心模块(损失函数等)
│   ├── utils.py                    # 工具函数
│   ├── generate_similarity.py      # 相似度生成
│   └── output/                     # 模型输出目录
└── img/                            # 图片资源
```

## 算法流程详解

### 1. 程序启动 (main.py)
**文件位置**: `src/main.py`

**主要功能**:
- 解析命令行参数
- 设置随机种子和GPU环境
- 初始化数据路径和输出目录

**关键代码流程**:
```python
# 第101-110行: 参数解析和环境设置
args = parser.parse_args()
set_seed(args.seed)
check_path(args.output_dir)
os.environ["CUDA_VISIBLE_DEVICES"] = args.gpu_id
args.data_file = args.data_dir + args.data_name + '.txt'
```

### 2. 数据预处理 (utils.py)
**文件位置**: `src/utils.py`

**主要功能**:
- 从原始数据文件读取用户序列
- 构建训练/验证/测试集的评分矩阵
- 处理用户-物品交互序列

**关键代码流程**:
```python
# main.py 第111-112行调用
user_seq, max_item, valid_rating_matrix, test_rating_matrix = \
    get_user_seqs(args.data_file)
```

### 3. 物品相似度模型构建 (models.py)
**文件位置**: `src/models.py`

**主要功能**:
- 离线相似度模型: 基于ItemCF、Item2Vec等方法
- 在线相似度模型: 基于共享物品嵌入

**关键组件**:
- `OfflineItemSimilarity` (第135-252行): 离线相似度计算
- `OnlineItemSimilarity` (第253行后): 在线相似度计算

**流程**:
```python
# main.py 第113-145行
# 1. 构建离线相似度模型
offline_similarity_model = OfflineItemSimilarity(...)
# 2. 构建在线相似度模型  
online_similarity_model = OnlineItemSimilarity(item_size=args.item_size)
```

### 4. 数据增强模块 (data_augmentation.py)
**文件位置**: `src/data_augmentation.py`

**主要增强方法**:
- **Crop** (第220-235行): 随机裁剪子序列
- **Mask** (第237-250行): 随机掩码物品
- **Reorder** (第252-267行): 随机重排子序列
- **Insert** (第149-186行): 插入相似物品
- **Substitute** (第189-218行): 替换为相似物品

**组合策略**:
- `CombinatorialEnumerate` (第5-41行): 组合枚举多种增强方法
- `Random` (第43-139行): 随机选择增强方法

### 5. 数据集构建 (datasets.py)
**文件位置**: `src/datasets.py`

**主要功能**:
- 构建对比学习数据集
- 为每个序列生成多个增强视图
- 处理训练/验证/测试数据

**关键流程**:
```python
# 第147-169行: 数据处理主逻辑
if self.data_type == "train":
    # 生成推荐任务数据
    cur_rec_tensors = self._data_sample_rec_task(...)
    # 生成对比学习数据对
    cf_tensors_list = []
    for i in range(total_augmentaion_pairs):
        cf_tensors_list.append(self._one_pair_data_augmentation(input_ids))
    return (cur_rec_tensors, cf_tensors_list)
```

### 6. 模型架构 (models.py)
**文件位置**: `src/models.py`

**核心模型**: `SASRecModel` (第15-133行)
- 物品嵌入层: `nn.Embedding(args.item_size, args.hidden_size)`
- 位置嵌入层: `nn.Embedding(args.max_seq_length, args.hidden_size)`
- Transformer编码器: `Encoder(args)`

**关键方法**:
- `transformer_encoder()` (第44-66行): Transformer编码
- `forward()` (第68-88行): 前向传播

### 7. 损失函数 (modules.py)
**文件位置**: `src/modules.py`

**对比学习损失**:
- **NCELoss** (第12-71行): 噪声对比估计损失
- **NTXent** (第72-105行): 归一化温度缩放交叉熵损失

**NCELoss核心逻辑**:
```python
# 第24-36行
def forward(self, batch_sample_one, batch_sample_two):
    sim11 = torch.matmul(batch_sample_one, batch_sample_one.T) / self.temperature
    sim22 = torch.matmul(batch_sample_two, batch_sample_two.T) / self.temperature
    sim12 = torch.matmul(batch_sample_one, batch_sample_two.T) / self.temperature
    # ... 构建对比学习目标
    nce_loss = self.criterion(logits, labels)
    return nce_loss
```

### 8. 训练器 (trainers.py)
**文件位置**: `src/trainers.py`

**核心训练器**: `CoSeRecTrainer` (第153-283行)

**训练流程** (第185-241行):
1. **推荐任务损失计算**:
   ```python
   # 第212-213行
   sequence_output = self.model.transformer_encoder(input_ids)
   rec_loss = self.cross_entropy(sequence_output, target_pos, target_neg)
   ```

2. **对比学习损失计算**:
   ```python
   # 第215-219行
   cl_losses = []
   for cl_batch in cl_batches:
       cl_loss = self._one_pair_contrastive_learning(cl_batch)
       cl_losses.append(cl_loss)
   ```

3. **联合损失优化**:
   ```python
   # 第221-226行
   joint_loss = self.args.rec_weight * rec_loss
   for cl_loss in cl_losses:
       joint_loss += self.args.cf_weight * cl_loss
   joint_loss.backward()
   self.optim.step()
   ```

### 9. 对比学习核心逻辑 (trainers.py)
**文件位置**: `src/trainers.py`

**方法**: `_one_pair_contrastive_learning()` (第168-183行)

**流程**:
1. 将增强数据对拼接: `cl_batch = torch.cat(inputs, dim=0)`
2. Transformer编码: `cl_sequence_output = self.model.transformer_encoder(cl_batch)`
3. 特征展平: `cl_sequence_flatten = cl_sequence_output.view(...)`
4. 计算对比损失: `cl_loss = self.cf_criterion(cl_output_slice[0], cl_output_slice[1])`

### 10. 训练主循环 (main.py)
**文件位置**: `src/main.py`

**训练流程** (第176-190行):
```python
for epoch in range(args.epochs):
    trainer.train(epoch)  # 训练一个epoch
    scores, _ = trainer.valid(epoch, full_sort=True)  # 验证
    early_stopping(np.array(scores[-1:]), trainer.model)  # 早停检查
    if early_stopping.early_stop:
        break
```

**在线相似度更新** (trainers.py 第67-70行):
```python
if epoch > self.args.augmentation_warm_up_epoches:
    print("refresh dataset with updated item embedding")
    self.train_dataloader = self.__refresh_training_dataset(self.model.item_embeddings)
```

### 11. 评估和测试 (trainers.py)
**文件位置**: `src/trainers.py`

**评估指标** (第98-112行):
- HIT@K: 命中率
- NDCG@K: 归一化折损累积增益

**测试流程**:
1. 加载最佳模型
2. 在测试集上进行全排序评估
3. 计算各项指标并保存结果

## 数据流向图

```
原始数据 (data/*.txt)
    ↓
数据预处理 (utils.py:get_user_seqs)
    ↓
相似度模型构建 (models.py:OfflineItemSimilarity/OnlineItemSimilarity)
    ↓
数据增强 (data_augmentation.py:各种增强方法)
    ↓
数据集构建 (datasets.py:RecWithContrastiveLearningDataset)
    ↓
模型训练 (trainers.py:CoSeRecTrainer)
    ├── 推荐损失 (cross_entropy)
    ├── 对比学习损失 (NCELoss/NTXent)
    └── 联合优化
    ↓
模型评估 (HIT@K, NDCG@K)
    ↓
结果保存 (output/)
```

## 关键参数配置

**数据增强参数**:
- `tao`: Crop比例 (默认0.2)
- `gamma`: Mask比例 (默认0.7)  
- `beta`: Reorder比例 (默认0.2)
- `insert_rate`: Insert比例 (默认0.3)
- `substitute_rate`: Substitute比例 (默认0.3)

**训练参数**:
- `rec_weight`: 推荐损失权重 (默认1.0)
- `cf_weight`: 对比学习损失权重 (默认0.1)
- `temperature`: 对比学习温度参数 (默认1.0)

**模型参数**:
- `hidden_size`: 隐藏层维度 (默认64)
- `num_hidden_layers`: Transformer层数 (默认2)
- `max_seq_length`: 最大序列长度 (默认50)

这个文档详细描述了CoSeRec算法从数据预处理到模型训练再到结果评估的完整流程，每一步都标明了对应的文件位置和关键代码行数。

## 核心文件流向过程详解

### 文件调用关系图
```
main.py (程序入口)
    ↓
    ├── utils.py (数据预处理)
    ├── models.py (模型和相似度)
    ├── datasets.py (数据集构建)
    │   └── data_augmentation.py (数据增强)
    ├── trainers.py (训练器)
    │   └── modules.py (损失函数)
    └── generate_similarity.py (相似度预计算)
```

### 详细流向过程

#### 1. main.py → utils.py
**调用时机**: 程序启动时的数据预处理阶段
```python
# main.py 第111-112行
user_seq, max_item, valid_rating_matrix, test_rating_matrix = \
    get_user_seqs(args.data_file)
```
**功能**: 从原始数据文件读取用户序列，构建评分矩阵

#### 2. main.py → models.py (相似度模型)
**调用时机**: 数据预处理完成后
```python
# main.py 第113-145行
# 构建离线相似度模型
offline_similarity_model = OfflineItemSimilarity(...)
# 构建在线相似度模型
online_similarity_model = OnlineItemSimilarity(...)
```
**功能**: 初始化物品相似度计算模型

#### 3. main.py → datasets.py
**调用时机**: 模型初始化后，构建训练数据
```python
# main.py 第148-160行
train_dataset = RecWithContrastiveLearningDataset(args, user_seq, data_type='train')
eval_dataset = RecWithContrastiveLearningDataset(args, user_seq, data_type='valid')
test_dataset = RecWithContrastiveLearningDataset(args, user_seq, data_type='test')
```
**功能**: 创建对比学习数据集

#### 4. datasets.py → data_augmentation.py
**调用时机**: 数据集初始化时和每次获取训练样本时
```python
# datasets.py 第27-46行 (初始化增强方法)
self.augmentations = {
    'crop': Crop(tao=args.tao),
    'mask': Mask(gamma=args.gamma),
    'reorder': Reorder(beta=args.beta),
    'substitute': Substitute(self.similarity_model, ...),
    'insert': Insert(self.similarity_model, ...),
    ...
}

# datasets.py 第162-163行 (生成增强数据)
for i in range(total_augmentaion_pairs):
    cf_tensors_list.append(self._one_pair_data_augmentation(input_ids))
```
**功能**: 对用户序列进行多种数据增强

#### 5. main.py → models.py (主模型)
**调用时机**: 数据集构建完成后
```python
# main.py 第163行
model = SASRecModel(args=args)
```
**功能**: 初始化SASRec主模型

#### 6. main.py → trainers.py
**调用时机**: 模型初始化后
```python
# main.py 第165-166行
trainer = CoSeRecTrainer(model, train_dataloader, eval_dataloader,
                         test_dataloader, args)
```
**功能**: 创建训练器，管理整个训练过程

#### 7. trainers.py → modules.py
**调用时机**: 训练器初始化时
```python
# trainers.py 第48行
self.cf_criterion = NCELoss(self.args.temperature, self.device)
```
**功能**: 初始化对比学习损失函数

#### 8. 训练过程中的文件交互

**8.1 trainers.py → models.py (前向传播)**
```python
# trainers.py 第212行 (推荐任务)
sequence_output = self.model.transformer_encoder(input_ids)

# trainers.py 第175行 (对比学习任务)
cl_sequence_output = self.model.transformer_encoder(cl_batch)
```

**8.2 trainers.py → modules.py (损失计算)**
```python
# trainers.py 第213行 (推荐损失)
rec_loss = self.cross_entropy(sequence_output, target_pos, target_neg)

# trainers.py 第181-182行 (对比学习损失)
cl_loss = self.cf_criterion(cl_output_slice[0], cl_output_slice[1])
```

**8.3 trainers.py → datasets.py (动态数据更新)**
```python
# trainers.py 第69行 (在线相似度更新后刷新数据集)
self.train_dataloader = self.__refresh_training_dataset(self.model.item_embeddings)
```

#### 9. generate_similarity.py (可选预计算)
**调用时机**: 训练前预计算物品相似度(可选)
**功能**: 预先计算并保存物品相似度矩阵，供OfflineItemSimilarity使用

### 执行时序图
```
程序启动
    ↓
main.py 解析参数
    ↓
utils.py 数据预处理
    ↓
models.py 构建相似度模型
    ↓
datasets.py 构建数据集
    ↓ (内部调用)
data_augmentation.py 数据增强
    ↓
models.py 构建主模型
    ↓
trainers.py 初始化训练器
    ↓ (内部调用)
modules.py 初始化损失函数
    ↓
开始训练循环:
    ├── datasets.py 获取批次数据
    │   └── data_augmentation.py 实时增强
    ├── models.py 前向传播
    ├── modules.py 计算损失
    ├── trainers.py 反向传播和优化
    └── 重复直到收敛
    ↓
trainers.py 模型评估
    ↓
保存结果到 output/
```

### 关键数据流
1. **原始数据** → utils.py → **用户序列**
2. **用户序列** → datasets.py + data_augmentation.py → **增强数据对**
3. **增强数据对** → models.py → **序列表示**
4. **序列表示** → modules.py → **对比学习损失**
5. **对比学习损失** + **推荐损失** → trainers.py → **联合优化**

这个流向过程展示了CoSeRec算法中各个文件模块的协作关系和数据传递路径。
